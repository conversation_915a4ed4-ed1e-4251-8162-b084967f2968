import { DataManageModel } from "./DataManageModel";
import { DataAndTag } from "./DataAndTag";
import { TagGroup } from "./TagGroup";
export class DataTag {
  id: GUID = "00000000-0000-0000-0000-000000000000";
  name?: string | null | undefined = null;
  dataManages?: DataManageModel[] | null | undefined = [];
  dataAndTag?: DataAndTag[] | null | undefined = [];
  groupId?: GUID = null;
  group?: TagGroup | null | undefined = null;
}
