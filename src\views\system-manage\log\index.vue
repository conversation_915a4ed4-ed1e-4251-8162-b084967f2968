<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-10-30 10:33:30
 * @LastEditors: 景 彡
-->
<template>
  <a-tabs centered>
    <a-tab-pane key="loginLog">
      <template #tab>
        <span>
          <c-icon-solution-outlined />
          登录日志
        </span>
      </template>
      <c-pro-table
        :columns="loginLogColumns"
        :api="api.UserManage.LoginLogList_GetAsync"
        row-key="id"
        immediate
        serial-number
        align="center"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'userName'">
            {{ record.userName }}
          </template>
        </template>
      </c-pro-table>
    </a-tab-pane>
    <a-tab-pane key="requestLog">
      <template #tab>
        <span>
          <c-icon-pull-request-outlined />
          请求日志
        </span>
      </template>
      <c-pro-table
        :columns="requestLogColumns"
        :api="api.UserManage.RequestLogList_GetAsync"
        row-key="id"
        immediate
        serial-number
        align="center"
      />
    </a-tab-pane>
  </a-tabs>
</template>

<script lang="ts" setup>
import { Api as api } from '@/api'
import { loginLogColumns, requestLogColumns } from './columns'

definePage({
  meta: {
    title: '日志管理',
  },
})
</script>
