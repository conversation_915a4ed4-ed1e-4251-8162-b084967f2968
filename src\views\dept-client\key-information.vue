<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2025-06-16 11:13:23
 * @LastEditors: 景 彡
-->
<template>
  <a-spin :spinning="spinning">
    <div v-if="articles.length > 0" class="mt-4">
      <div v-for="item in articles" :key="item.id" class="relative mb4 mb4 border border-gray-100 rounded-2xl bg-white p-4 shadow-md">
        <div v-show="item.isChina" class="absolute right-0 top-0 rounded-bl-2xl rounded-tr-2xl bg-red-100 px-2 py-1 text-xs text-red-500">涉我</div>

        <div class="text-xs text-gray-500">{{ item.sourceCountry }} <span class="ml-2">{{ item.source }}</span> </div>
        <h3 class="mt-2 text-sm font-medium">{{ item.titleCn }}</h3>
        <p class="line-clamp-3 mt-2 text-xs text-gray-500">
          {{ item.contentCn }}
        </p>
        <div class="mt-3 flex items-center justify-between">
          <div class="text-xs text-gray-400">{{ dateTime(item.time, 'YYYY-MM-DD') }}</div>
          <div class="rounded-2xl bg-red-500 px-2 py-1 text-xs text-white">热门</div>
        </div>
      </div>
    </div>
    <a-empty v-else />
  </a-spin>
</template>

<script lang='ts' setup>
import type { DataManageModelHotPageModel } from '@/api/models'
import { Api as api } from '@/api'
import { message } from 'ant-design-vue'

const spinning = ref(false)

const articles = ref<DataManageModelHotPageModel[]>([])

async function getData() {
  spinning.value = true
  try {
    articles.value = await api.Statistices.GetHotDataManageAsync({ pageIndex: 9999 })
    spinning.value = false
  }
  catch (error: any) {
    message.error(`获取数据失败${error.message}`)
    spinning.value = false
  }
}

onMounted(() => {
  getData()
})
</script>

<style scoped>

</style>
