import type { User } from '@/stores/modules/user'
import type { LoginData } from '@/types/user-login'
import { message } from 'ant-design-vue'
import axios from 'axios'
import { Api } from './index'

export const oidcPasswordFlowSetting = {
  grant_type: 'password',
  username: '',
  password: '',
  client_id: 'Asean-DSPark_Manage',
  client_secret: 'E048F5FE7C3FCC909416813D7C4C0F0D',
  scope: 'api offline_access',
  refresh_token: '',
}

export class UserService {
  static async login(data: LoginData) {
    try {
      const user = (await axios.request({
        method: 'POST',
        url: '/connect/token',
        // baseURL: import.meta.env.VITE_APP_PROXY_TARGET,
        data: { ...oidcPasswordFlowSetting, ...data, refresh_token: '' },
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        responseType: 'json',
      })) as any

      const userStore = useUserStore()

      const time = new Date(
        new Date().getTime() + user.data.expires_in * 1000,
      )

      userStore.setToken({
        refresh_token: user.data.refresh_token!,
        expires_time: time.toString(),
        token: user.data.access_token!,
      })

      await UserService.getUserInfo()
      eventBus.emit('login-success')
    }
    catch (err: any) {
      let mes = ''
      if (err.response.data.error_description) {
        mes = (err.response.data.error_description)
      }
      else {
        mes = (err?.message ?? '登录失败')
      }
      message.error(mes)
      throw new Error(mes)
    }
  }

  static async simulation(_userId: string) {
    throw new Error('error')
  }

  static async stopSimulation() {
    throw new Error('error')
  }

  static async logout(_isApi = false) {
    const userStore = useUserStore()
    const tabsStore = useTabsStore()
    const keepAliveStore = useKeepAliveStore()
    if (userStore.token) {
      await axios.request({
        method: 'POST',
        url: '/connect/logout',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Bearer ${userStore.token}`,
        },
        data: null,
        responseType: 'json',
      })
    }
    userStore.clear()
    tabsStore.$reset()
    keepAliveStore.$reset()
  }

  static async mpLogin() {
    throw new Error('error')
  }

  /**
   * 获取用户信息并将信息存入本地
   */
  static async getUserInfo(simulation = false) {
    const userStore = useUserStore()
    const user = await Api.CurrentUser.Me_GetAsync()
    const data = {
      info: user,
      refresh_token: userStore.refresh_token,
      expires_time: userStore.expires_time,
      info_token: userStore.token,
      remember: false,
      name: user.name!,
      userName: user.userName!,
      userId: user.id!,
      roles: user.roles || [],
    } as User
    userStore.setUser(data, simulation ? 'add' : 'overlay')
    useAppStore().getSysSetting()
  }

  static async updateUserInfo() {
    const userStore = useUserStore()
    const user = await Api.CurrentUser.Me_GetAsync()
    const data = {
      info: user,
      refresh_token: userStore.refresh_token,
      expires_time: userStore.expires_time,
      info_token: userStore.token,
      remember: false,
      name: user.name!,
      userName: user.userName!,
      userId: user.id!,
      roles: user.roles || [],
    } as User
    useAppStore().getSysSetting()
    userStore.updateUser(data)
  }
}
