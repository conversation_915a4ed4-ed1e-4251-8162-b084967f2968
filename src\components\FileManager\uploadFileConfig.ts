import { FileType } from '@/api/models'

interface UploadOptions {
  Directory: string
  Extensions: string
  LimitedSize: string
}

interface IUploadConfig extends Record<FileType, UploadOptions> { }

export class UploadConfig implements IUploadConfig {
  isMatch(path: string, types?: FileType[]) {
    const extension = path.split('.').pop()?.toLowerCase()
    if (!extension)
      return false

    if (types) {
      return types.some((type) => {
        const validExtensions = this[type].Extensions.split('|')
        return validExtensions.includes(extension)
      })
    }

    return Object.values(FileType).some((type) => {
      const validExtensions = this[type as any as FileType].Extensions.split('|')
      return validExtensions.includes(extension)
    })
  }

  getExtensions(types?: FileType[]): string {
    const validExtensions = (types || Object.values(FileType)).map((type) => {
      return this[type as any as FileType].Extensions.split('|').map(v => `.${v}`)
    })

    return validExtensions.join(',')
  }

  [FileType.未知] = {
    Directory: '未知',
    Extensions: '',
    LimitedSize: '1MB',
  };

  [FileType.图片] = {
    Directory: 'images',
    Extensions: 'jpg|jpeg|png|gif|bmp|ico|webp',
    LimitedSize: '1MB',
  };

  [FileType.文档] = {
    Directory: 'documents',
    Extensions: 'doc|docx|pdf|txt|rtf|xls|xlsx|xlsm|et|wps|csv',
    LimitedSize: '20MB',
  };

  [FileType.压缩] = {
    Directory: 'compressed',
    Extensions: 'zip|rar',
    LimitedSize: '500MB',
  };

  [FileType.视频] = {
    Directory: 'videos',
    Extensions: 'mp4|avi|mov|wmv',
    LimitedSize: '500MB',
  };

  [FileType.音频] = {
    Directory: 'audios',
    Extensions: 'mp3|wav|aac',
    LimitedSize: '30MB',
  };

  [FileType.幻灯片] = {
    Directory: 'presentation',
    Extensions: 'ppt|pptx',
    LimitedSize: '10MB',
  }
}
