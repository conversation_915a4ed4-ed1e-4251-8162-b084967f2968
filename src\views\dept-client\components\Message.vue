<!--
 * @Description:
 * @Author: luckymiaow
 * @Date: 2023-05-15 14:50:21
 * @LastEditors: 景 彡
-->
<!-- 通知消息 -->
<template>
  <div>
    <a-badge c-inherit :dot="warninData.length > 0 || !!Object.keys(feedbackRemindMsg).length" @click="drawerVisible = true">
      <BellOutlined class="cursor-pointer text-1.6em c-#fff" />
    </a-badge>
    <a-drawer
      v-model:open="drawerVisible"
      placement="right"
      width="600"
      :closable="true"
      title="消息提醒"
      @close="drawerVisible = false"
    >
      <a-tabs v-model:active-key="activeTab" @change="onTabChange">
        <a-tab-pane key="warning" tab="预警提醒">
          <div v-if="warninData.length === 0" class="text-center text-gray-400">暂无预警提醒</div>
          <div class="space-y-2">
            <div v-for="item in warninData" :key="String(item.id ?? item.riskWarningId)" class="flex cursor-pointer justify-between" @click="toLink(item.riskWarningId)">
              <span>{{ item.queryTag }}：</span>
              <b class="c-primary">{{ item.count }}</b>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="feedback" tab="反馈提醒">
          <div class="mb-2 flex items-center justify-between">
            <span />
            <a-button type="link" @click="feedbackRecordVisible = true">查看反馈记录</a-button>
          </div>
          <div v-if="Object.keys(feedbackRemindMsg).length === 0" class="text-center text-gray-400">暂无反馈提醒</div>
          <div v-else class="space-y-2">
            <div v-for="(msg, key) in Object.entries(feedbackRemindMsg)" :key="String(key ?? '')" class="flex cursor-pointer justify-between border-b p-2" @click="showDetail(String(msg[0] ?? ''))">
              <span class="c-text-secondary">{{ key + 1 }}、</span>
              <b class="c-primary">{{ msg[1] }}</b>
            </div>
          </div>
        </a-tab-pane>
        <template #rightExtra>
          <a-button class="text-base c-error" type="link" danger :icon="h(ToolOutlined)" @click="onFeedback">问题反馈</a-button>
        </template>
      </a-tabs>
    </a-drawer>
    <a-modal v-model:open="feedbackRecordVisible" title="反馈记录" width="700px" :footer="null">
      <div class="mb-2 flex gap-2">
        <a-radio-group v-model:value="status" @change="onStatusChange">
          <a-radio :value="undefined">全部</a-radio>
          <a-radio :value="FeedbackStatus.待处理">未处理</a-radio>
          <a-radio :value="FeedbackStatus.处理中">处理中</a-radio>
          <a-radio :value="FeedbackStatus.已解决">已处理</a-radio>
        </a-radio-group>
      </div>
      <div v-if="feedbackList.length === 0" class="text-center text-gray-400">暂无反馈记录</div>
      <div v-else class="space-y-2">
        <div v-for="item in feedbackList" :key="String(item.id)" class="flex flex-col gap-1 border-b p-2">
          <div class="flex items-center justify-between">
            <span v-if="item.dataId" class="cursor-pointer text-primary hover:underline" @click.stop="toArticle(item.dataId)">{{ item.title || '无标题' }}</span>
            <span class="text-xs text-gray-400">{{ dateTime(item.createdTime) }}</span>
          </div>
          <div class="flex gap-4 text-xs text-gray-500">
            <span>处理状态：{{ feedbackStatusText(item.status) }}</span>
          </div>
          <div class="text-sm text-gray-700">反馈内容：{{ item.description }}</div>
          <div v-if="item.response" class="text-xs text-green-600">回复：{{ item.response }}</div>
          <a-button @click="showDetail(String(item.id))">查看详情</a-button>
        </div>
        <a-pagination
          :current="currentPage"
          :page-size="pageSize"
          :total="total"
          :show-size-changer="false"
          class="mt-2 text-center"
          @change="onPageChange"
        />
      </div>
    </a-modal>
    <a-modal v-model:open="detailModalVisible" title="反馈详情" width="80%" :footer="null">
      <div v-if="selectedFeedback">
        <p><b>类型：</b>{{ FeedbackType[selectedFeedback.type] }}</p>
        <p><b>描述：</b>{{ selectedFeedback.description }}</p>
        <p><b>状态：</b>{{ FeedbackStatus[selectedFeedback.status] }}</p>
        <p><b>提交时间：</b>{{ dateTime(selectedFeedback.createdTime) }}</p>
        <p><b>处理回复：</b>{{ selectedFeedback.response }}</p>
        <template v-if="selectedFeedback.dataManageModel">
          <a-divider>
            <div class="center gap2">
              关联文章信息    <a-switch
                v-model:checked="isZh"
                checked-children="中文"
                un-checked-children="原文"
              />
            </div>
          </a-divider>
          <p><b>标题：</b>{{ isZh ? selectedFeedback.dataManageModel.titleCn || selectedFeedback.dataManageModel.title : selectedFeedback.dataManageModel.title || selectedFeedback.dataManageModel.titleCn }}</p>
          <p v-if="selectedFeedback.dataManageModel.author"><b>作者：</b>{{ selectedFeedback.dataManageModel.author }}</p>
          <p v-if="selectedFeedback.dataManageModel.time"><b>时间：</b>{{ selectedFeedback.dataManageModel.time }}</p>
          <p v-if="selectedFeedback.dataManageModel.source"><b>来源：</b>{{ selectedFeedback.dataManageModel.source }}</p>
          <p v-if="isZh ? selectedFeedback.dataManageModel.contentCn : selectedFeedback.dataManageModel.content">
            <b>内容：</b>{{ isZh ? selectedFeedback.dataManageModel.contentCn || selectedFeedback.dataManageModel.content : selectedFeedback.dataManageModel.content || selectedFeedback.dataManageModel.contentCn }}
          </p>
          <p v-if="selectedFeedback.dataManageModel.tag && selectedFeedback.dataManageModel.tag.length">
            <b>标签：</b>
            <span v-for="(tag, index) in selectedFeedback.dataManageModel.tag" :key="String(tag.id || tag.name || index)" style="margin-right: 8px;">{{ tag.name }}</span>
          </p>
        </template>
      </div>
    </a-modal>
    <FeedbackModal v-model:visible="showFeedback" @success="showFeedback = false" />
  </div>
</template>

<script setup lang="ts">
import type { RiskWarningReadViewModel, UserFeedbackPageView } from '@/api/models'
import { Api as api } from '@/api'
import { FeedbackStatus, FeedbackType } from '@/api/models'
import notifySound from '@/assets/提示音.mp3'
import { BellOutlined, ToolOutlined } from '@ant-design/icons-vue'
import { message, notification } from 'ant-design-vue'
import { h, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'

const { warninData, getCount, startInterval, stopInterval } = useWarningRemind()

const { feedbackList, status, currentPage, pageSize, total, onPageChange, onStatusChange, getFeedbackList } = useFeedbackRemind()

// setup中调用
const { feedbackRemindMsg } = useFeedbackRemindNotification()

const feedbackRecordVisible = ref(false)
const drawerVisible = ref(false)
const activeTab = ref<string>('warning')
const router = useRouter()
const detailModalVisible = ref(false)
const selectedFeedback = ref<any>(null)
const isZh = ref(true)

watch(feedbackRecordVisible, (v) => {
  if (v) {
    getFeedbackList()
  }
})
const showFeedback = ref(false)

function onFeedback() {
  showFeedback.value = true
}

function playNotifySound() {
  const audio = new Audio(notifySound)
  audio.play().catch((e) => {
    console.warn('音频播放失败', e)
  })
}

onMounted(() => {
  const unlock = () => {
    const audio = new Audio('data:audio/wav;base64,UklGRiQAAABXQVZFZm10IBAAAAABAAEAIlYAAESsAAACABAAZGF0YQAAAAA=')
    audio.play().finally(() => {
      window.removeEventListener('click', unlock)
    })
  }
  window.addEventListener('click', unlock)
})

async function toLink(id: string | GUID) {
  try {
    await api.RiskWarning.MarkAsRead_GetAsync({ id: id! })
    getCount()
  }
  catch (error: any) {
    message.error(error.message)
  }
  const routePath = router.resolve({
    path: '/dept-client/risk',
    query: { tagId: id },
  }).href
  window.open(routePath, '_blank')
}

function onTabChange(key: string | number) {
  if (key === 'warning') {
    getCount()
    startInterval()
  }
  else if (key === 'feedback') {
    // pollFeedbackRemindMsg() // This line is now handled by useFeedbackRemindNotification
    stopInterval()
  }
}

function toArticle(articleId: string | GUID | undefined | null) {
  if (!articleId) {
    return
  }
  const routePath = router.resolve({
    path: '/dept-client/detail',
    query: { id: String(articleId) },
  }).href
  window.open(routePath, '_blank')
}

function feedbackStatusText(status: number) {
  const map: { [key: number]: string } = {
    [FeedbackStatus.待处理]: '未处理',
    [FeedbackStatus.处理中]: '处理中',
    [FeedbackStatus.已解决]: '已处理',
  }
  return map[status] ?? '未知'
}

async function showDetail(id: string | undefined) {
  if (!id) {
    return
  }
  try {
    const detail = await api.Feedbacks.UserGetFeedbackById_GetAsync({ feedbackId: String(id) })
    selectedFeedback.value = detail
    detailModalVisible.value = true
  }
  catch (error: any) {
    message.error(error.message)
  }
}

// 预警提醒hook
function useWarningRemind() {
  const warninData = ref<RiskWarningReadViewModel[]>([])
  let interval: any = null

  async function getCount() {
    warninData.value = await api.RiskWarning.GetReadAsync()
  }

  function startInterval() {
    interval = setInterval(() => {
      getCount()
    }, 60000)
  }

  function stopInterval() {
    if (interval) {
      clearInterval(interval)
    }
  }

  onMounted(() => {
    getCount()
    startInterval()
  })
  onUnmounted(() => {
    stopInterval()
  })

  return { warninData, getCount, startInterval, stopInterval }
}

// 反馈提醒hook（增强：分页+状态）
function useFeedbackRemind() {
  const feedbackList = ref<UserFeedbackPageView[]>([])
  const status = ref<FeedbackStatus | undefined>(undefined) // undefined=全部，0=未处理，1=处理中，2=已处理
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)

  async function getFeedbackList() {
    try {
      const res = await api.Feedbacks.GetUserFeedbacksByUserAsync({
        status: status.value,
        offset: (currentPage.value - 1) * pageSize.value,
        limit: pageSize.value,
      })
      feedbackList.value = res.items || []
      total.value = res.totals ?? 0
    }
    catch (error: any) {
      message.error(error.message)
    }
  }

  function onPageChange(page: number) {
    currentPage.value = page
    getFeedbackList()
  }

  function onStatusChange(e: any) {
    status.value = e.target.value
    currentPage.value = 1
    getFeedbackList()
  }

  return { feedbackList, getFeedbackList, status, currentPage, pageSize, total, onPageChange, onStatusChange }
}

// 反馈提醒notification hook
function useFeedbackRemindNotification() {
  const feedbackRemindMsg = ref<Record<string, string>>({})
  let lastRemindMsg: Record<string, string> = {}
  let remindInterval: any = null

  async function pollFeedbackRemindMsg() {
    try {
      const newMsg = await api.Feedbacks.GetResponseAsync()
      const newKeys = Object.keys(newMsg)
      const oldKeys = Object.keys(lastRemindMsg)
      let hasNew = false
      const notifyList: Array<{ id: string, content: string }> = []
      for (const key of newKeys) {
        if (!oldKeys.includes(key) || lastRemindMsg[key] !== newMsg[key]) {
          hasNew = true
          notifyList.push({ id: key ?? '', content: newMsg[key] ?? '' })
        }
      }
      if (hasNew && notifyList.length > 0) {
        notifyList.forEach((item) => {
          playNotifySound()
          notification.success({
            message: '反馈提醒',
            key: item.id,
            description: h(
              'a',
              {
                style: 'color: #1677ff; cursor: pointer;',
                onClick: async () => {
                  await showDetail(item.id)
                  notification.close(item.id)
                },
              },
              String(item.content ?? ''),
            ),
            placement: 'bottomRight',
            duration: 30,
          })
          showSystemNotification('反馈提醒', String(item.content ?? ''))
        })
      }
      feedbackRemindMsg.value = newMsg
      lastRemindMsg = { ...newMsg }
    }
    catch {
      // 可选：notification.error({ ... })
    }
  }

  onMounted(() => {
    pollFeedbackRemindMsg()
    remindInterval = setInterval(pollFeedbackRemindMsg, 60000)
  })
  onUnmounted(() => {
    if (remindInterval) {
      clearInterval(remindInterval)
    }
  })

  return { feedbackRemindMsg }
}
</script>

<style scoped></style>
