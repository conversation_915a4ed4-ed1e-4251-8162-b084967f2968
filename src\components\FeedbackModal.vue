<template>
  <a-modal v-model:open="modalVisible" title="问题反馈" :footer="null" width="500px" @cancel="handleCancel">
    <a-form layout="vertical" :model="feedbackForm" @finish="submitFeedback">
      <a-form-item label="问题类型" name="type" required>
        <a-select v-model:value="feedbackForm.type" placeholder="请选择问题类型">
          <a-select-option :value="FeedbackType.翻译错误">翻译错误</a-select-option>
          <a-select-option :value="FeedbackType.内容空白">文章内容空白</a-select-option>
          <a-select-option :value="FeedbackType.内容缺失">文章内容缺失</a-select-option>
          <a-select-option :value="FeedbackType.弹窗报错">弹窗报错</a-select-option>
          <a-select-option :value="FeedbackType.其他问题">其他</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="问题描述" name="description">
        <a-textarea v-model:value="feedbackForm.description" :rows="4" placeholder="请简要描述您遇到的问题" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" block html-type="submit">提交反馈</a-button>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { Api as api } from '@/api'
import { FeedbackType } from '@/api/models'
import { message } from 'ant-design-vue'
import { ref, watch } from 'vue'

const props = defineProps<{
  visible: boolean
  dataId?: string
}>()

const emit = defineEmits(['update:visible', 'success'])

const modalVisible = ref(props.visible)
watch(() => props.visible, val => (modalVisible.value = val))
watch(modalVisible, val => emit('update:visible', val))

const feedbackForm = ref({
  type: FeedbackType.内容空白,
  description: '',
  dataId: props.dataId,
})

watch(
  () => props.dataId,
  (val) => {
    feedbackForm.value.dataId = val
  },
)

function handleCancel() {
  modalVisible.value = false
}

async function submitFeedback() {
  try {
    await api.Feedbacks.SubmitFeedback_PostAsync(feedbackForm.value)
    message.success('反馈成功')
    modalVisible.value = false
    emit('success')
  }
  catch (error: any) {
    message.error(error.message)
  }
}
</script>
