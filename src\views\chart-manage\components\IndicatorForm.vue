<template>
  <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
    <a-form-item label="数值">
      <a-input-number
        v-model:value="editingIndicatorCell.value"
        style="width: 100%"
        placeholder="请输入数值"
      />
    </a-form-item>

    <a-form-item label="数据来源URL">
      <a-input
        v-model:value="editingIndicatorCell.url"
        placeholder="请输入数据来源URL"
      />
    </a-form-item>

    <a-form-item label="源文件">
      <Upload v-model:value="editingIndicatorCell.attachments" auto-list />
    </a-form-item>

    <a-form-item label="是否官方">
      <a-select v-model:value="editingIndicatorCell.via" placeholder="请选择数据来源类型">
        <a-select-option value="官方数据">官方数据</a-select-option>
        <a-select-option value="非官方数据">非官方数据</a-select-option>
        <a-select-option value="后台计算">后台计算</a-select-option>
      </a-select>
    </a-form-item>

    <a-form-item label="更新类型">
      <a-select v-model:value="editingIndicatorCell.updateType" placeholder="请选择更新类型">
        <a-select-option value="错误修复">错误修复</a-select-option>
        <a-select-option value="新指标">新指标</a-select-option>
        <a-select-option value="后台计算">后台计算</a-select-option>
      </a-select>
    </a-form-item>

    <a-form-item label="采集说明">
      <a-textarea
        v-model:value="editingIndicatorCell.remarks"
        :rows="3"
        placeholder="请输入采集说明"
      />
    </a-form-item>

    <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
      <div class="flex gap4">
        <a-button type="primary" ghost @click="copyIndicatorData">
          <template #icon><c-icon-copy-outlined /></template>
          复制
        </a-button>
        <a-button type="primary" :disabled="!hasClipboardData" @click="pasteIndicatorData"> <template #icon><c-icon-snippets-outlined /></template>粘贴</a-button>
      </div>
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import { ChartDatasetCell } from '@/api/models'
import { message } from 'ant-design-vue'
import { computed, ref } from 'vue'

const editingIndicatorCell = defineModel('value', { default: new ChartDatasetCell() })

// 剪贴板数据存储
const clipboardData = ref<ChartDatasetCell | null>(null)

// 检查是否有剪贴板数据
const hasClipboardData = computed(() => clipboardData.value !== null)

// 复制当前编辑对象
function copyIndicatorData() {
  try {
    // 深拷贝当前编辑的数据
    clipboardData.value = JSON.parse(JSON.stringify(editingIndicatorCell.value))
    message.success('数据已复制到剪贴板')
  }
  catch (error) {
    console.error('复制数据失败:', error)
    message.error('复制数据失败')
  }
}

// 粘贴剪贴板数据到当前编辑对象
function pasteIndicatorData() {
  if (!clipboardData.value) {
    message.warning('剪贴板中没有数据')
    return
  }

  try {
    // 深拷贝剪贴板数据到当前编辑对象
    const pastedData = JSON.parse(JSON.stringify(clipboardData.value))

    // 保留当前的 id 和 updatedAt，只复制业务数据
    const currentUpdatedAt = editingIndicatorCell.value.updatedAt

    // 更新编辑对象
    Object.assign(editingIndicatorCell.value, pastedData, {
      updatedAt: currentUpdatedAt, // 保持原有更新时间
    })

    message.success('数据已从剪贴板粘贴')
  }
  catch (error) {
    console.error('粘贴数据失败:', error)
    message.error('粘贴数据失败')
  }
}
</script>

<style scoped>

</style>
