<template>
  <div class="h-full flex flex-col">
    <div class="mt4 h-0 flex flex-1 flex-col bg-bg-container p4">
      <!-- 过滤规则选择区域 -->
      <div class="mb-4 flex items-center gap-4">
        <div class="flex items-center gap-2">
          <span class="text-sm font-medium">过滤规则:</span>
          <a-select
            v-model:value="selectedProjectId"
            placeholder="请选择过滤规则"
            style="width: 200px"
            :loading="projectLoading"
            @change="onProjectChange"
          >
            <a-select-option
              v-for="project in projects"
              :key="project.id"
              :value="project.id"
            >
              {{ project.title }}
            </a-select-option>
          </a-select>
        </div>
        <a-button type="primary" :loading="tableLoading" @click="refreshData">
          刷新数据
        </a-button>
      </div>

      <!-- 数据列表 -->
      <div class="flex-1">
        <!-- 列表头部 - 紧凑版 -->
        <div class="mb-2 flex items-center justify-between rounded-md from-blue-50 to-indigo-50 bg-gradient-to-r p-2 shadow-sm">
          <div class="flex items-center space-x-2">
            <div class="h-6 w-6 flex items-center justify-center rounded-full bg-blue-600">
              <svg class="h-3 w-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div>
              <h2 class="text-base text-gray-900 font-semibold">GDELT 全球事件新闻</h2>
              <p v-if="selectedProject" class="text-xs text-gray-600">
                {{ selectedProject.title }} | {{ newsData.length }} 条数据
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-1">
            <a-button
              size="small"
              type="primary"
              ghost
              :icon="h(ReloadOutlined)"
              :loading="tableLoading"
              @click="refreshData"
            >
              刷新
            </a-button>
            <a-button
              size="small"
              type="primary"
              :icon="h(ExportOutlined)"
              @click="exportData"
            >
              导出
            </a-button>
          </div>
        </div>

        <!-- 顶部分页 -->
        <div v-if="newsData.length > 0" class="mb-4 flex justify-center">
          <a-pagination
            v-model:current="pagination.current"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :show-size-changer="true"
            :show-quick-jumper="true"
            :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
            size="small"
            @change="onPageChange"
            @show-size-change="onPageSizeChange"
          />
        </div>

        <a-spin :spinning="tableLoading">
          <!-- 新闻列表 - 参考policy-news简洁风格 -->
          <div v-if="newsData.length > 0">
            <article
              v-for="(item, index) in newsData"
              :key="item.id"
              class="mb-4 flex overflow-hidden rounded bg-white shadow transition-shadow hover:shadow-md"
            >
              <div class="relative flex-1 p-4">
                <!-- 标题行 -->
                <div class="mb-2 cursor-pointer hover:text-blue-500" @click="openLink(item.to_link_url || '')">
                  <h3 class="mb-1 text-base font-medium">
                    {{ index + 1 }}、{{ item.link_text }}
                  </h3>
                  <p v-if="item.link_text_cn" class="text-sm text-gray-600">
                    {{ item.link_text_cn }}
                  </p>
                </div>

                <!-- 信息行 -->
                <div class="mt-3 flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-500">{{ formatDate(item.date) }}</span>
                    <a-tag :color="getLanguageColor(item.langcode)" size="small">
                      {{ getLanguageName(item.langcode) }}
                    </a-tag>
                    <span v-if="item.tags && item.tags.length > 0" class="text-sm text-gray-500">
                      标签：
                      <span v-for="(tag, tagIndex) in item.tags.slice(0, 2)" :key="tag">
                        {{ tag }}<span v-if="tagIndex < Math.min(item.tags.length, 2) - 1">、</span>
                      </span>
                      <span v-if="item.tags.length > 2">等{{ item.tags.length }}个</span>
                    </span>
                  </div>

                  <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-500">
                      原文链接：<a :href="item.to_link_url || ''" target="_blank" class="text-blue-600 hover:text-blue-800">查看详情</a>
                    </span>
                    <button
                      class="ml-2 rounded p-1 text-gray-400 transition-colors hover:bg-gray-100 hover:text-gray-600"
                      title="查看详情"
                      @click="showDetail(item)"
                    >
                      <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </article>
          </div>

          <!-- 空状态 -->
          <div v-if="newsData.length === 0" class="flex flex-col items-center justify-center py-12">
            <div class="mb-3 h-16 w-16 flex items-center justify-center rounded-full bg-gray-100">
              <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 class="mb-1 text-base text-gray-900 font-medium">暂无数据</h3>
            <p class="text-sm text-gray-500">请选择过滤规则或刷新数据</p>
          </div>
        </a-spin>
        <!-- 底部分页 -->
        <div v-if="newsData.length > 0" class="mt-6 flex justify-center">
          <a-pagination
            v-model:current="pagination.current"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :show-size-changer="true"
            :show-quick-jumper="true"
            :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
            size="small"
            @change="onPageChange"
            @show-size-change="onPageSizeChange"
          />
        </div>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:open="detailVisible"
      title="事件详情"
      width="800px"
      :footer="null"
    >
      <div v-if="selectedRecord" class="space-y-4">
        <div>
          <h3 class="mb-2 text-lg font-medium">标题</h3>
          <p class="text-gray-800">{{ selectedRecord.link_text }}</p>
          <p v-if="selectedRecord.link_text_cn" class="mt-1 text-gray-600">
            中文: {{ selectedRecord.link_text_cn }}
          </p>
        </div>

        <div>
          <h3 class="mb-2 text-lg font-medium">发布信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <span class="font-medium">发布日期:</span>
              <span class="ml-2">{{ formatDate(selectedRecord.date) }}</span>
            </div>
            <div>
              <span class="font-medium">语言:</span>
              <span class="ml-2">{{ getLanguageName(selectedRecord.langcode) }}</span>
            </div>
          </div>
        </div>

        <div v-if="selectedRecord.tags && selectedRecord.tags.length > 0">
          <h3 class="mb-2 text-lg font-medium">标签</h3>
          <div class="flex flex-wrap gap-2">
            <a-tag
              v-for="tag in selectedRecord.tags"
              :key="tag"
              color="blue"
            >
              {{ tag }}
            </a-tag>
          </div>
        </div>

        <div>
          <h3 class="mb-2 text-lg font-medium">链接</h3>
          <a
            :href="selectedRecord.to_link_url || ''"
            target="_blank"
            class="break-all text-blue-600 hover:text-blue-800"
          >
            {{ selectedRecord.to_link_url }}
          </a>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import type { FilteprojectView, FilteresultView } from '@/.generated/models'
import { Api } from '@/api'
import { ExportOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'

// 页面定义
definePage({
  meta: {
    title: '谷歌事件库',
  },
})

// 响应式数据
const projects = ref<FilteprojectView[]>([])
const selectedProjectId = ref<number>()
const selectedProject = computed(() =>
  projects.value.find(p => p.id === selectedProjectId.value),
)
const projectLoading = ref(false)
const tableLoading = ref(false)
const detailVisible = ref(false)
const selectedRecord = ref<FilteresultView>()
const newsData = ref<FilteresultView[]>([])

// 分页数据
const pagination = ref({
  current: 1,
  pageSize: 20,
  total: 0,
})

// 加载新闻数据
async function loadNewsData() {
  // if (!selectedProjectId.value) {
  //   newsData.value = []
  //   return
  // }

  try {
    tableLoading.value = true
    const result = await Api.Gdelt.GetFilteresultAsync({
      project_id: selectedProjectId.value,
      limit: pagination.value.pageSize,
      skip: (pagination.value.current - 1) * pagination.value.pageSize,
    })

    newsData.value = result || []
    pagination.value.total = (result?.length || 0) >= pagination.value.pageSize ? 1000 : (result?.length || 0)
  }
  catch (error) {
    console.error('获取过滤结果失败:', error)
  }
  finally {
    tableLoading.value = false
  }
}

// 加载过滤规则
async function loadProjects() {
  projectLoading.value = true
  try {
    const result = await Api.Gdelt.GetFilteprojectRolesAsync()
    projects.value = result || []

    if (projects.value.length > 0 && !selectedProjectId.value) {
      const firstProject = projects.value.find(v => v.title === '东盟/全球动态')
      if (firstProject?.id) {
        selectedProjectId.value = firstProject.id
      }
    }
  }
  catch (error) {
    console.error('获取过滤规则失败:', error)
    message.warning('无法连接到服务器，使用演示数据')
  }
  finally {
    projectLoading.value = false
  }
}

// 事件处理函数
function onProjectChange(value: number) {
  selectedProjectId.value = value
  pagination.value.current = 1
  loadNewsData()
}

function refreshData() {
  loadNewsData()
}

function exportData() {
  // 导出数据功能
  message.info('导出功能开发中...')
}

function openLink(url?: string) {
  if (url) {
    window.open(url, '_blank')
  }
}

function showDetail(item: FilteresultView) {
  selectedRecord.value = item
  detailVisible.value = true
}

// 分页事件处理
function onPageChange(page: number, pageSize: number) {
  pagination.value.current = page
  pagination.value.pageSize = pageSize
  loadNewsData()
}

function onPageSizeChange(_current: number, size: number) {
  pagination.value.current = 1
  pagination.value.pageSize = size
  loadNewsData()
}

// 工具函数
function formatDate(date: any) {
  if (!date)
    return '-'
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

function getLanguageName(langcode?: string | null) {
  const languageMap: Record<string, string> = {
    'en': '英语',
    'zh': '中文',
    'zh-cn': '简体中文',
    'zh-tw': '繁体中文',
    'ja': '日语',
    'ko': '韩语',
    'th': '泰语',
    'vi': '越南语',
    'id': '印尼语',
    'ms': '马来语',
    'my': '缅甸语',
    'km': '柬埔寨语',
    'lo': '老挝语',
    'tl': '菲律宾语',
    'bn': '孟加拉语',
  }
  return languageMap[langcode || ''] || langcode || '未知'
}

function getLanguageColor(langcode?: string | null) {
  const colorMap: Record<string, string> = {
    'en': 'blue',
    'zh': 'red',
    'zh-cn': 'red',
    'zh-tw': 'volcano',
    'ja': 'purple',
    'ko': 'geekblue',
    'th': 'green',
    'vi': 'orange',
    'id': 'cyan',
    'ms': 'lime',
    'my': 'gold',
    'km': 'magenta',
    'lo': 'pink',
    'tl': 'yellow',
    'bn': 'gray',
  }
  return colorMap[langcode || ''] || 'default'
}

// 监听选中项目变化
watch(selectedProjectId, (newValue) => {
  if (newValue) {
    pagination.value.current = 1
    loadNewsData()
  }
})

// 生命周期
onMounted(() => {
  loadProjects()
})
</script>

<style scoped>
/* 政企风格样式 - 紧凑版 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.news-card:hover {
  transform: translateY(-1px);
  box-shadow:
    0 8px 25px -5px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: #3b82f6;
}

/* 简化的核心样式 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 组件样式优化 */
:deep(.ant-btn) {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

:deep(.ant-tag) {
  border-radius: 12px;
  font-weight: 500;
  border: none;
}

:deep(.ant-pagination) {
  .ant-pagination-item-active {
    background: #3b82f6;
    border-color: #3b82f6;
  }
}
</style>
