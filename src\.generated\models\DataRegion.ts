import { DataManageModel } from "./DataManageModel";
import { DataAndRegion } from "./DataAndRegion";
import { RegionGroup } from "./RegionGroup";
/**地区*/
export class DataRegion {
  id?: string | null | undefined = null;
  name?: string | null | undefined = null;
  dataManages?: DataManageModel[] | null | undefined = [];
  /**涉及地区*/
  dataAndRegion?: DataAndRegion[] | null | undefined = [];
  groupId?: GUID = null;
  group?: RegionGroup | null | undefined = null;
}
