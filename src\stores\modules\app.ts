import type { AssemblySizeType, ThemeColor, ThemeConfigProps } from '@/types/interfaces'
import { SystemInfo } from '@/api/models'
import { darkThemes, themes } from '@/theme'
import { deepCopy } from '@/utils/util'
/*
 * @Description: ^_^
 * @Author: sharebravery
 * @Date: 2023-04-10 11:37:46
 */
import { defineStore } from 'pinia'

const defaultThemeConfig: ThemeConfigProps = {
  maximize: false,
  layout: 'transverse',
  themeColor: { ...themes['#2E709F']! },
  isDark: false,
  darkMenu: false,
  isGrey: false,
  isWeak: false,
  isCollapse: false,
  breadcrumb: false,
  breadcrumbIcon: true,
  tabs: true,
  tabsIcon: true,
  footer: true,
  isDarkBySystem: false,
}

export interface IAppState {
  language: string
  showLanguage: boolean
  themeConfig: ThemeConfigProps
  _themeColor: {
    type: 'light' | 'dark'
    color: ThemeColor
  }
  showThemeConfig: boolean
  assemblySize: AssemblySizeType
  showAssemblySize: boolean
  routerLoading: boolean
  setting: SystemInfo
}

export const useAppStore = defineStore('app', {
  state: (): IAppState => ({
    language: '',
    themeConfig: deepCopy(defaultThemeConfig),
    showThemeConfig: true,
    assemblySize: 'default',
    routerLoading: false,
    showLanguage: false,
    showAssemblySize: false,
    _themeColor: {
      type: 'light',
      color: deepCopy(defaultThemeConfig.themeColor),
    },
    setting: new SystemInfo(),
  }),

  actions: {
    updateLanguage(language: string) {
      this.language = language
    },
    setThemeConfig(themeConfig: ThemeConfigProps) {
      this.themeConfig = themeConfig
    },
    // 字体大小
    setAssemblySizeSize(assemblySize: AssemblySizeType) {
      this.assemblySize = assemblySize
    },
    convertThemeColors(isDark: boolean) {
      if (this.themeConfig.isDark === isDark)
        return
      this.themeConfig.isDark = isDark
      const temp = deepCopy(this.themeConfig.themeColor)
      if (this.themeConfig.isDark)
        this.themeConfig.themeColor = this._themeColor.type === 'dark' ? deepCopy(this._themeColor.color) : deepCopy(Object.values(darkThemes)[0]!)
      else
        this.themeConfig.themeColor = this._themeColor.type === 'light' ? deepCopy(this._themeColor.color) : deepCopy(Object.values(themes)[0]!)

      this._themeColor = {
        type: this.themeConfig.isDark ? 'light' : 'dark',
        color: temp,
      }
    },
    updateIsDarkBySystem() {
      if (!this.themeConfig.isDarkBySystem)
        return
      const themeMedia = window.matchMedia('(prefers-color-scheme: light)')
      this.convertThemeColors(!themeMedia.matches)
    },

    /**
     * 系统配置
     */
    async getSysSetting() {
      // this.setting = await SystemBaseInfo.GetAsync()
    },

    reset() {
      Object.assign(this, {
        language: '',
        themeConfig: deepCopy(defaultThemeConfig),
        showThemeConfig: true,
        assemblySize: 'default',
        routerLoading: false,
        showLanguage: false,
        showAssemblySize: false,
        _themeColor: {
          type: 'light',
          color: deepCopy(defaultThemeConfig.themeColor),
        },
      })
    },
  },
})
