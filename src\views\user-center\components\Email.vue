<!--
 * @Author: Ztq
 * @Date: 2023-06-01 11:35:03
 * @LastEditors: Ztq
 * @LastEditTime: 2023-06-02 09:31:33
 * @Description:
 * @FilePath: \ch2-template-vue\src\views\user-center\components\Email.vue
-->
<template>
  <c-modal
    :open="visible"
    :style="{ top: '0px' }"
    :closable="true"
    title="绑定邮箱"
    :destroy-on-close="true"
    width="800px"
    @cancel="cancel()"
  >
    <template #footer>
      <a-button type="default" @click="cancel()">
        取消
      </a-button>
      <a-button type="primary" @click="handleOk()">
        确认修改
      </a-button>
    </template>
    <a-form ref="formRef" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" :model="emailData" :rules="rules">
      <a-form-item label="新邮箱" name="email">
        <a-space style="width: 100%">
          <div>
            <c-input v-model:value="emailData.email" style="width: 400px" />
          </div>
          <a-button type="primary" :disabled="!isValid || isWait" @click="sendCaptcha">
            <span v-if="!isWait">获取验证码</span>
            <span v-else style="display: flex">
              <a-statistic-countdown
                :value="deadline"
                format="ss"
                value-style="font-size:16px;margin-right:5px;"
                @finish="isWait = false"
              />
              <span>秒后重新发送</span>
            </span>
          </a-button>
        </a-space>
      </a-form-item>

      <a-form-item label="验证码" name="captcha">
        <c-input
          v-model:value="emailData.captcha"
          placeholder="请输入验证码"
          :maxlength="16"
          style="width: 400px"
        />
      </a-form-item>
    </a-form>
  </c-modal>
</template>

<script setup lang="ts">
import type { FormInstance } from 'ant-design-vue'
import type { Rule, RuleObject } from 'ant-design-vue/es/form'
import { emailReg } from '@/utils/regExp'
import {} from '@ant-design/icons-vue'
import { Form, message } from 'ant-design-vue'
// import { Api as api } from '@/api';
import { reactive, ref } from 'vue'

class LoginByEmailModel {
  /* 邮箱 */
  email: string = ''

  /* 验证码 */
  captcha: string = ''
}

const emailData = ref(new LoginByEmailModel())

const visible = ref(false)
const isWait = ref(false)
const isValid = ref(false)
const deadline = ref<number>(Date.now() + 1000 * 60)

// 校验表单
const formRef = ref<FormInstance>()

// 定义表单校验对象
const { useForm } = Form

// 校验规则
const rules = reactive<Record<string, RuleObject | RuleObject[]>>({
  email: [{ required: true, validator: validateEmail, trigger: ['change', 'blur'] }],
  captcha: [{ required: true, message: '请输入邮箱验证码', trigger: 'blur' }],
})

const { resetFields } = useForm(emailData, rules, {})

function cancel() {
  resetFields()
  visible.value = false
}

function handleOk() {
  formRef.value?.validate().then((res) => {
    if (res) {
      // api.Users.ChangePassword_PostAsync(formData);
      message.success('修改邮箱成功！')
      cancel()
    }
  })
}

function sendCaptcha() {
  isWait.value = true
  deadline.value = Date.now() + 1000 * 5
}

function validateEmail(_rule: Rule, value: string) {
  isValid.value = false
  if (!value)
    return Promise.reject(new Error('请输入邮箱'))

  if (!emailReg.test(value))
    return Promise.reject(new Error('请输入正确的邮箱'))

  isValid.value = true
  return Promise.resolve()
}

defineExpose({
  visible,
})
</script>
