<template>
  <div ref="containerRef" class="h-full w-full flex flex-col bg-bg-container p-6">
    <div class="mb-4">
      <a-button type="primary" class="rounded px-5 py-2" @click="openAddModal">新增标签</a-button>
    </div>
    <RecycleScroller
      :items="tags"
      :item-size="90"
      :item-secondary-size="220"
      :grid-items="gridItems"
      key-field="id"
      class="h0 w-full flex-1 overflow-auto"
    >
      <template #default="{ item: tag }">
        <div class="box-border h-[90px] cursor-pointer p2">
          <div
            class="group relative h-full w-full flex flex-col items-center justify-center border border-primary/10 rounded-2xl bg-primary-bg shadow transition-all duration-200 hover:bg-primary-bg/80 hover:shadow-lg hover:-translate-y-1"
            @click="viewArticles(tag)"
          >
            <div class="absolute right-4 top-4 z-10">
              <a-dropdown>
                <a class="ant-dropdown-link" @click.prevent>
                  <MoreOutlined class="text-xl c-primary hover:c-primary-hover" />
                </a>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click.stop="openEditModal(tag)">
                      <EditOutlined class="c-primary" /> 编辑
                    </a-menu-item>
                    <a-menu-item @click.stop="deleteTag(tag)">
                      <DeleteOutlined class="c-error" /> 删除
                    </a-menu-item>
                    <a-menu-item @click.stop="viewArticles(tag)">
                      <FileTextOutlined class="c-primary" /> 查看文章
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
            <div class="w-full truncate text-center text-2xl c-primary font-bold">{{ tag.name }}</div>
          </div>
        </div>
      </template>
    </RecycleScroller>
    <!-- 新增/编辑标签弹窗 -->
    <a-modal
      v-model:open="showModal"
      :title="editTag ? '编辑标签' : '新增标签'"
      ok-text="保存"
      cancel-text="取消"
      centered
      destroy-on-close
      @ok="saveTag"
      @cancel="closeModal"
    >
      <a-input v-model:value="modalTagName" placeholder="请输入标签名" class="mb-5" />
    </a-modal>
    <!-- 查看文章抽屉 -->
    <a-drawer
      :open="showDrawer"
      :title="currentTag ? `标签“${currentTag.name}”下的文章` : ''"
      placement="right"
      width="60%"
      @close="closeDrawer"
    >
      <a-list
        :data-source="articles"
        bordered
        class="mb-6 flex-1 overflow-y-auto"
        style="max-height: 60vh;"
      >
        <template #renderItem="{ item }">
          <a-list-item
            class="cursor-pointer transition hover:bg-gray-50"
            @click="openArticleModal(item.id)"
          >
            <a-list-item-meta
              :title="item.title"
              :description="`作者：${item.author || '未知'} 时间：${dateFormat(item.time)} 来源：${item.source || '未知'}\n${(item.contentCn || item.content || '').slice(0, 40)}${(item.contentCn || item.content || '').length > 40 ? '...' : ''}`"
            />
          </a-list-item>
        </template>
      </a-list>
      <template #footer>
        <a-button type="primary" class="rounded px-5 py-1.5" @click="closeDrawer">关闭</a-button>
      </template>
    </a-drawer>
    <ViewArticleModal
      v-model:visible="viewArticleModalVisible"
      :article-id="viewArticleId"
    />
  </div>
</template>

<script lang="ts" setup>
import type { DataHotTagView, DataManageModel } from '@/api/models'
import { Api } from '@/api'
import ViewArticleModal from '@/components/ViewArticleModal.vue'
import { DeleteOutlined, EditOutlined, FileTextOutlined, MoreOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { onBeforeUnmount, onMounted, ref } from 'vue'
import { RecycleScroller } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

definePage({
  meta: {
    title: '热门标签管理',
    icon: 'TagOutlined',
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '热门标签管理',
        local: true,
        icon: 'TagOutlined',
        order: 5,
      },
    },
  },
})

const tags = ref<DataHotTagView[]>([])
const showModal = ref(false)
const editTag = ref<DataHotTagView | null>(null)
const modalTagName = ref('')

const showDrawer = ref(false)
const currentTag = ref<DataHotTagView | null>(null)
const articles = ref<DataManageModel[]>([])

const containerRef = ref<HTMLElement | null>(null)
const cardWidth = 220
const cardGap = 0 // 可根据实际样式调整
const gridItems = ref(3)

function calcGridItems() {
  if (!containerRef.value)
    return
  const width = containerRef.value.offsetWidth
  gridItems.value = Math.max(1, Math.floor(width / (cardWidth + cardGap)))
}

let resizeObserver: ResizeObserver | null = null
onMounted(() => {
  calcGridItems()
  resizeObserver = new ResizeObserver(calcGridItems)
  if (containerRef.value)
    resizeObserver.observe(containerRef.value)
  window.addEventListener('resize', calcGridItems)
  fetchTags()
})
onBeforeUnmount(() => {
  if (resizeObserver && containerRef.value)
    resizeObserver.unobserve(containerRef.value)
  window.removeEventListener('resize', calcGridItems)
})

async function fetchTags() {
  tags.value = await Api.DataHotTags.GetAllTagsAsync()
}

function openAddModal() {
  editTag.value = null
  modalTagName.value = ''
  showModal.value = true
}

function openEditModal(tag: DataHotTagView) {
  editTag.value = tag
  modalTagName.value = tag.name || ''
  showModal.value = true
}

function closeModal() {
  showModal.value = false
}

async function saveTag() {
  if (!modalTagName.value.trim()) {
    message.error('标签名不能为空')
    return
  }
  if (editTag.value) {
    // 编辑
    await Api.DataHotTags.UpdateTag_GetAsync({ tagId: String(editTag.value.id), newName: modalTagName.value })
    message.success('标签编辑成功')
  }
  else {
    // 新增
    await Api.DataHotTags.CreateTag_GetAsync({ tagName: modalTagName.value })
    message.success('标签新增成功')
  }
  showModal.value = false
  fetchTags()
}

function deleteTag(tag: DataHotTagView) {
  Modal.confirm({
    title: `确定要删除标签“${tag.name}”吗？`,
    okText: '删除',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      await Api.DataHotTags.DeleteTag_GetAsync({ tagId: String(tag.id) })
      message.success('删除成功')
      fetchTags()
    },
  })
}

async function viewArticles(tag: DataHotTagView) {
  currentTag.value = tag
  articles.value = await Api.DataHotTags.GetArticlesByTagAsync({ tagId: String(tag.id) })
  showDrawer.value = true
}

function closeDrawer() {
  showDrawer.value = false
}

const viewArticleId = ref<string | undefined>(undefined)
const viewArticleModalVisible = ref(false)
function openArticleModal(articleId: string) {
  viewArticleId.value = articleId
  viewArticleModalVisible.value = true
}
</script>
