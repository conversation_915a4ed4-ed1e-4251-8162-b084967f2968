<template>
  <c-pro-table
    ref="tableRef"
    :api="Api.Feedbacks.GetUserFeedbacks_PostAsync"
    :columns="columns"
    row-key="id"

    bordered immediate serial-number
  >
    <template #bodyCell="{ record, column }">
      <template v-if="column.key === 'action'">
        <a @click="viewFeedbackDetail(record)">详情</a>
        <template v-if="record.status === FeedbackStatus.待处理">
          <a-divider type="vertical" />
          <a @click="viewFeedbackDetail(record)">处理</a>
        </template>
      </template>
    </template>
  </c-pro-table>

  <!-- 反馈详情弹窗 -->
  <a-modal v-model:open="detailModalVisible" title="反馈详情" width="80%" :footer="null">
    <div v-if="selectedFeedback">
      <p><b>类型：</b>{{ FeedbackType[selectedFeedback.type] }}</p>
      <p><b>描述：</b>{{ selectedFeedback.description }}</p>
      <p><b>状态：</b>{{ FeedbackStatus[selectedFeedback.status] }}</p>
      <p><b>提交时间：</b>{{ dateTime(selectedFeedback.createdTime) }}</p>
      <p><b>处理回复：</b>{{ selectedFeedback.response }}</p>
      <!-- 快捷回复 -->
      <template v-if="selectedFeedback.status === FeedbackStatus.待处理">
        <a-divider>处理反馈</a-divider>
        <a-form @submit.prevent="handleProcessFeedback">
          <a-form-item label="处理状态">
            <a-select v-model:value="processForm.newStatus" style="width: 120px">
              <a-select-option :value="FeedbackStatus.已解决">已处理</a-select-option>
              <a-select-option :value="FeedbackStatus.处理中">处理中</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="回复内容">
            <a-auto-complete
              v-model:value="processForm.customResponse"
              :options="replyTemplates.map(t => ({ value: t }))"
              placeholder="请选择或输入回复内容"
              style="width: 100%"
            >
              <a-textarea :rows="3" />
            </a-auto-complete>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleProcessFeedback">提交回复</a-button>
          </a-form-item>
        </a-form>
      </template>
      <!-- 智库文章相关内容展示 -->
      <template v-if="selectedFeedback.dataManageModel">
        <a-divider>
          <div class="center gap2">
            关联文章信息    <a-switch
              v-model:checked="isZh"
              checked-children="中文"
              un-checked-children="原文"
            />
          </div>
        </a-divider>
        <ViewArticleModal view :article-id="selectedFeedback.dataManageModel.id!" />
      </template>
    </div>
  </a-modal>

  <!-- 处理反馈弹窗 -->
  <!-- 已移除 -->
</template>

<script setup lang="ts">
import type { UserFeedbackPageView, UserFeedbackView } from '@/../src/.generated/models'
import type { ColumnProps } from 'ch2-components/lib/pro-table/types'
import { FeedbackStatus, FeedbackType } from '@/../src/.generated/models'
import { Api } from '@/api'
import { message } from 'ant-design-vue'
import { reactive, ref } from 'vue'

definePage({
  meta: {
    title: '反馈管理',
    icon: 'MessageOutlined',
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '反馈管理',
        local: true,
        icon: 'MessageOutlined',
        order: 6,
      },
    },
  },
})

const tableRef = useTemplateRef('tableRef')

const columns: ColumnProps<UserFeedbackPageView>[] = [
  { title: '类型', dataIndex: 'type', key: 'type', enum: FeedbackType, width: 120 },
  { title: '描述', dataIndex: 'description', key: 'description', ellipsis: true },
  { title: '状态', dataIndex: 'status', key: 'status', width: 100, enum: FeedbackStatus, search: { el: 'enum-select', method: 'GET', attrs: { enum: FeedbackStatus } } },
  { title: '提交用户', dataIndex: 'feedbackUserName' },
  { title: '提交时间', dataIndex: 'createdTime', key: 'createdTime', dateFormat: true, width: 180 },
  { title: '操作', key: 'action', width: 120 },
  { title: '用户', dataIndex: 'feedbackUserId', isShow: false, search: { el: 'select', method: 'GET', attrs: { allowClear: true, api: Api.UserManage.GetUserViewListRefAsync, fieldNames: { label: 'name', value: 'id' } } } },

]

// 详情弹窗
const detailModalVisible = ref(false)
const selectedFeedback = ref<UserFeedbackView | null>(null)
const isZh = ref(true)
// 预设回复模板
const replyTemplates = [
  '感谢您的反馈，我们会尽快处理。',
  '您的问题已收到，相关人员会跟进。',
  '感谢您的建议，我们会持续优化产品。',
  '该问题已修复，请您刷新后重试。',
  '如有更多问题，欢迎随时联系。',
]
async function viewFeedbackDetail(record: UserFeedbackPageView) {
  try {
    const detail = await Api.Feedbacks.GetFeedbackByIdAsync({ feedbackId: record.id as string })
    selectedFeedback.value = detail
    detailModalVisible.value = true
  }
  catch {
    message.error('获取反馈详情失败')
  }
}

// 处理反馈弹窗
const processForm = reactive<{ feedbackId: string, newStatus?: FeedbackStatus, customResponse?: string }>({ feedbackId: '' })
async function handleProcessFeedback() {
  if (!selectedFeedback.value)
    return
  processForm.feedbackId = selectedFeedback.value.id as string
  try {
    await Api.Feedbacks.ProcessFeedback_PostAsync({
      feedbackId: processForm.feedbackId,
      newStatus: FeedbackStatus.已解决,
      customResponse: processForm.customResponse,
    })
    message.success('处理成功')
    detailModalVisible.value = false
    processForm.customResponse = ''
    tableRef.value?.refresh()
  }
  catch {
    message.error('处理反馈失败')
  }
}
</script>

<style scoped>
.feedback-manage {
  padding: 24px;
  background: #fff;
  min-height: 100%;
}
</style>
