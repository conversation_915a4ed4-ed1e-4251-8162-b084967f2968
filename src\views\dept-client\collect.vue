<template>
  <div class="m-auto min-h-[calc(100vh-160px)] flex gap-5 p5 container">
    <a-affix :offset-top="80">
      <div class="h-[calc(100vh-160px)] w-80 rounded bg-bg-container p6 pr1 shadow-md">
        <div class="h-[calc(100%-42px)] overflow-y-auto pr5">
          <div class="flex items-center justify-between">
            <h3>我的收藏</h3>
            <a-dropdown>
              <c-icon-dash-outlined />
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a href="javascript:;" @click="onAdd(null, false)">新增</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div v-for="item of listData" :key="item.id" class="ml-4 mt6">
            <div class="flex items-center justify-between">
              <h4>{{ item.name }}</h4>
              <a-dropdown>
                <c-icon-dash-outlined />
                <template #overlay>
                  <a-menu>
                    <a-menu-item>
                      <a href="javascript:;" @click="onAdd(item.id, true)">新增</a>
                    </a-menu-item>
                    <a-menu-item>
                      <a href="javascript:;" @click="onEdit(item)">编辑</a>
                    </a-menu-item>
                    <a-menu-item>
                      <a href="javascript:;" class="c-error" @click="onDel(item)">删除</a>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
            <div v-for="(p, index) of item.children" :key="index" class="ml-4 mt4">
              <div class="group relative mt2 flex cursor-pointer justify-between text-xs c-gray-600 hover:text-primary-active" @click="getArticlesList(p, item.name, p.name)">
                <div>{{ p.name }}</div>
                <div>{{ p.articleCount }}</div>
                <div class="absolute right-0 top-0 hidden bg-#fff group-hover:block">
                  <c-icon-edit-outlined class="mr-4 c-primary" @click.stop="onEdit(p)" />
                  <c-icon-close-outlined class="c-error" @click.stop="onDel(p)" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="h-42px cursor-pointer rounded-lg p-2 text-base c-text hover:(bg-[rgba(52,150,206,.8)] c-#fff)" @click="onOpenNote"><c-icon-book-outlined class="pr-2" />我的笔记</div>
      </div>
    </a-affix>

    <div class="w-full rounded bg-bg-container p-4 shadow-md">
      <c-pro-table ref="proTableRef" row-key="id" operation :show-tool-btn="false" :row-selection="rowSelection" :columns="columns" :api="api.FavoriteArticles.GetArticlesInFolder_PostAsync" :get-params="{ folderId: currentFolderId }">
        <template #header>
          <div class="w-100% flex justify-between">
            <div class="">
              <div>全部收藏 - {{ currentFolderName }}</div>
              <div class="mt2 text-xs text-gray-500">{{ currentFolderCount }}项</div>
            </div>
            <div>
              <a-button :disabled="rowSelection.selectedRowKeys.length <= 0" type="primary" ghost :icon="h(ExportOutlined)" @click="exportArticle">导出</a-button>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'title'">
            <div> <a>{{ record.title }}</a></div>
            <div>{{ record.titleCn }}</div>
          </template>
          <template v-if="column.dataIndex === 'region'">
            <a-tag
              v-for="tag in record.region"
              :key="tag.id"
              color="green"
            >
              {{ tag }}
            </a-tag>
          </template>
          <template v-if="column.dataIndex === 'userTags'">
            <template v-if="record.userTags.length > 0">
              <a-tag
                v-for="tag in record.userTags"
                :key="tag.id"
                color="green"
                closable
                @close="userTagsHandleClose(tag, record)"
              >
                {{ tag.name }}
              </a-tag>
            </template>
            <template v-else />
            <a-button type="dashed" danger size="small" @click="onMark(record)">
              <c-icon-plus-outlined /> 添加
            </a-button>
          </template>
        </template>
        <template #operation="{ record }">
          <a-button type="link" danger @click="delRows(record)">
            移除
          </a-button>
          <!-- <a-button type="link" @click="onMark(record)">
            标记
          </a-button> -->
        </template>
      </c-pro-table>
    </div>
  </div>

  <a-drawer
    v-model:open="open"
    title="新增目录/收藏"
    placement="left"
    size="large"
  >
    <c-pro-form
      v-model:value="form"
      :descriptions="{ column: 1, bordered: true }"
      :fields="fields"
      :label-col="{ style: { width: '100px' } }"
      layout="inline"
      @finish="onSave"
    >
      <template #footer>
        <a-descriptions-item label="操作">
          <a-button type="primary" html-type="submit">
            保存
          </a-button>
        </a-descriptions-item>
      </template>
    </c-pro-form>
  </a-drawer>

  <a-drawer
    v-model:open="editOpen"
    title="编辑目录/收藏夹"
    placement="left"
    size="large"
  >
    <c-pro-form
      v-model:value="editForm"
      :descriptions="{ column: 1, bordered: true }"
      :fields="editFields"
      :label-col="{ style: { width: '100px' } }"
      layout="inline"
      @finish="onEditSave"
    >
      <template #footer>
        <a-descriptions-item label="操作">
          <a-button type="primary" html-type="submit">
            保存
          </a-button>
        </a-descriptions-item>
      </template>
    </c-pro-form>
  </a-drawer>

  <a-drawer
    v-model:open="noteOpen"
    width="100%"
    :footer-style="{ textAlign: 'right' }"
  >
    <template #title>
      <h2>笔记与报告</h2>
    </template>
    <template #footer>
      <a-button key="back" @click="noteOpen = false">关闭</a-button>
    </template>
    <Note />
  </a-drawer>

  <a-modal v-model:open="markOpen" title="标记文章">
    <c-pro-form
      v-model:value="markForm"
      :descriptions="{ column: 1, bordered: true }"
      :fields="markFields"
      :label-col="{ style: { width: '100px' } }"
      layout="inline"
      @finish="markHandleOk"
    >
      <template #footer>
        <a-descriptions-item label="操作">
          <a-button type="primary" html-type="submit">
            保存
          </a-button>
        </a-descriptions-item>
      </template>
    </c-pro-form>
    <template #footer>
      <a-button key="back" @click="markOpen = false">关闭</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import type { DataManageModelPageView, FolderWithCountDto, UserTagRes } from '@/api/models'
import type { FormField } from 'ch2-components/lib/pro-form/types'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import { Api as api } from '@/api'
import { DataType, FolderType } from '@/api/models'
import { ExportOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import Note from './components/Note.vue'

const { editOpen, editForm, editFields, onEdit, onEditSave } = editHook()

const { noteOpen, onOpenNote } = noteHook()

const { markOpen, markForm, markFields, markHandleOk, onMark, userTagsHandleClose } = markHook()

const columns: ColumnProps[] = [
  {
    title: '修改时间',
    dataIndex: 'time',
    dateFormat: 'YYYY-MM-DD HH:mm',
  },
  {
    title: '标题',
    dataIndex: 'title',
    // search: {
    //   el: 'input',
    //   attrs: {
    //     placeholder: '请输入关键词',
    //   },
    // },
  },
  {
    title: '标题',
    dataIndex: 'keyword',
    search: {
      el: 'input',
      attrs: {
        placeholder: '请输入标题',
      },
      method: 'GET',
    },
    isShow: false,
  },
  {
    title: '关键词',
    dataIndex: 'keyword',
    search: {
      el: 'input',
      attrs: {
        placeholder: '请输入关键词',
      },
      method: 'POST',
    },
    isShow: false,
  },
  {
    title: '文章类型',
    dataIndex: 'dataType',
    search: {
      el: 'enum-select',
      attrs: {
        placeholder: '请选择',
        enum: DataType,
      },

      method: 'POST',
    },
    isShow: false,
  },
  {
    title: '涉及地域',
    dataIndex: 'region',
  },
  {
    title: '标记',
    dataIndex: 'userTags',
    search: {
      el: 'select',
      emptyValue: [],
      attrs: {
        api: api.UserTags.FindByUser_GetAsync,
        fieldNames: { label: 'name', value: 'id' },
        mode: 'multiple',
      },
      method: 'POST',
    },
  },
]

const open = ref(false)

const form = ref({ name: '', remark: '', type: FolderType.目录, parentId: '' })

const optionsData = ref<{ label: string, value: string }[]>([])

const fields = computed<FormField<{
  name?: string
  remark?: string
  type?: FolderType
  parentId?: string
}>[]>(() => [
  {
    label: '父级',
    prop: 'parentId',
    el: 'select',
    attrs: { options: optionsData.value },
  },
  {
    label: '类型',
    prop: 'type',
    el: 'enum-select',
    attrs: { enum: FolderType, disabled: true },
  },
  {
    label: '名称',
    prop: 'name',
    el: 'input',
    attrs: {},
  },
  // {
  //   label: '备注',
  //   prop: 'remark',
  //   el: 'textarea',
  //   attrs: {},
  // },
])

function onAdd(id: string | null, judge: boolean) {
  if (judge)
    form.value.type = FolderType.收藏夹
  else form.value.type = FolderType.目录
  form.value.parentId = id || ''
  open.value = true
}

async function onSave() {
  try {
    await api.FavoriteArticles.CreateFolder_GetAsync(form.value)

    message.success('保存成功')
    getData()
    open.value = false
  }
  catch (error: any) {
    Modal.warning({
      title: '保存失败',
      content: error.message,
    })
  }
}

const listData = ref<FolderWithCountDto[]>([])

async function getData() {
  try {
    listData.value = await api.FavoriteArticles.GetAllFoldersWithArticleCountAsync()
    optionsData.value = listData.value.map(item => ({
      label: item.name,
      value: item.id,
    }))
  }
  catch (error: any) {
    message.error(`获取数据失败${error.message}`)
  }
}

const rowSelection = ref({
  selectedRowKeys: [],
  selectedRows: [],
  onChange(selectedRowKeys: never[], selectedRows: never[]) {
    rowSelection.value.selectedRowKeys = selectedRowKeys
    rowSelection.value.selectedRows = selectedRows
  },
})

async function exportArticle() {
  useDownload(() => api.DataManageModels.ExportMultiple_PostAsync(rowSelection.value.selectedRowKeys), `${dateTime(new Date(), 'YYYY-MM-DD')}-收藏文章导出`)
}

const proTableRef = useTemplateRef('proTableRef')

const currentFolderId = ref(Guid.empty)

const currentFolderName = ref('')

const currentFolderCount = ref(0)

function getArticlesList(record: FolderWithCountDto, name1: string, name2: string) {
  currentFolderCount.value = record.articleCount
  currentFolderName.value = `${name1} - ${name2}`
  currentFolderId.value = record.id as string
  nextTick(() => {
    proTableRef.value?.search()
  })
}

function onDel(row: FolderWithCountDto) {
  Modal.confirm({
    title: '删除',
    content: `确定要删除【${row.name}】吗？`,
    okText: '删除',
    cancelText: '取消',
    onOk: async () => {
      try {
        await api.FavoriteArticles.DeleteFolder_GetAsync({ folderId: row.id! })
        message.success('删除成功')
        getData()
      }
      catch (error: any) {
        Modal.warning({
          title: '删除失败',
          content: error.message,
        })
      }
    },
  })
}

function delRows(row: DataManageModelPageView) {
  Modal.confirm({
    title: '移出收藏夹',
    content: `确定把【${row.title}】移除出收藏夹吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await api.FavoriteArticles.DeleteDataFromFolder_GetAsync({ folderId: currentFolderId.value!, dataManageModelId: row.id! })
        message.success('移除成功')
        proTableRef.value?.search()
      }
      catch (error: any) {
        Modal.warning({
          title: '移除失败',
          content: error.message,
        })
      }
    },
  })
}

function editHook() {
  const editForm = ref({ name: '', remark: '', id: '' })

  const editOpen = ref(false)

  const editFields = ref<FormField<{
    name?: string
  }>[]>([
    {
      label: '名称',
      prop: 'name',
      el: 'input',
      attrs: {},
    },
  ])

  function onEdit(record: FolderWithCountDto) {
    editForm.value = { name: record.name!, remark: '', id: record.id! }
    editOpen.value = true
  }

  async function onEditSave() {
    try {
      await api.FavoriteArticles.EditFolder_GetAsync(editForm.value)
      message.success('保存成功')
      getData()
      editOpen.value = false
    }
    catch (error: any) {
      Modal.warning({
        title: '保存失败',
        content: error.message,
      })
    }
  }

  return { editOpen, editForm, editFields, onEdit, onEditSave }
}

function noteHook() {
  const noteOpen = ref(false)

  function onOpenNote() {
    noteOpen.value = true
  }

  return { noteOpen, onOpenNote }
}

function markHook() {
  const markForm = ref({ dataId: '', userTagId: undefined, userTagName: '' })

  const markFields = ref<FormField<{
    userTagId?: string | undefined
    userTagName?: string
  }>[]>([
    {
      label: '选择标记',
      prop: 'userTagId',
      el: 'select',
      attrs: {
        api: api.UserTags.FindByUser_GetAsync,
        fieldNames: { label: 'name', value: 'id' },
      },
    },
    {
      label: '自定义标记',
      prop: 'userTagName',
      el: 'input',
      attrs: {},
    },
  ])

  const markOpen = ref(false)

  async function markHandleOk() {
    try {
      await api.FavoriteArticles.AddUserTagToData_GetAsync(markForm.value)
      message.success('保存成功')
      markOpen.value = false
      proTableRef.value?.search()
    }
    catch (error: any) {
      message.error(`保存失败${error.message}`)
    }
  }

  function onMark(row: DataManageModelPageView) {
    markForm.value.dataId = row.id as string
    markOpen.value = true
  }

  async function userTagsHandleClose(tags: UserTagRes, row: DataManageModelPageView) {
    await api.FavoriteArticles.RemoveUserTagFromData_GetAsync({ dataId: row.id!, userTagId: tags.id }).then((res) => {
      if (res) {
        message.success('移除成功')
        proTableRef.value?.search()
      }
    })
  }

  return { markOpen, markForm, markFields, markHandleOk, onMark, userTagsHandleClose }
}

onMounted(() => {
  getData()
})
</script>

<style scoped>
:deep(.header-button-lf) {
  width: 100%;
}
</style>
