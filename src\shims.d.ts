declare interface Window {
  // extend the window
}

// with unplugin-vue-markdown, markdown files can be treated as Vue components
declare module '*.md' {
  import type { DefineComponent } from 'vue'

  const component: DefineComponent<object, object, any>
  export default component
}

declare module '*.vue' {
  import type { DefineComponent } from 'vue'

  const component: DefineComponent<object, object, any>
  export default component
}

declare module 'vue-virtual-scroller';

declare module 'virtual:file-layout-router-module' {

  export function useFileRouter(): {
    localRoutes: RouteRecordRaw[]
    localRoutesNamedMap: Record<string, RouteRecordRaw>
    allRoutesNamedMap: Record<string, RouteRecordRaw>
    allRoutes: RouteRecordRaw[]
  }
}

declare module 'unplugin-vue-router/data-loaders' {

  export function DataLoaderPlugin(...args: any) { }

}
declare module 'sortablejs';
