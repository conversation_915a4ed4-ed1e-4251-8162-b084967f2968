<template>
  <a-spin :spinning="spinning">
    <a-tree
      v-model:checked-keys="checkedKeys" v-model:expanded-keys="expandedKeys" checkable :tree-data="treeData"
    />
  </a-spin>
</template>

<script setup lang="ts">
import type { RouterItem } from '@/types/interfaces'
import { Api as api } from '@/api'
import { routerAllMenu } from '@/router'
import { message } from 'ant-design-vue'

const { menuConfig = [], roleId } = defineProps<{
  menuConfig?: string[]
  roleId: string
}>()

const checkedKeys = ref<string[]>([])

const expandedKeys = ref<string[]>(['0'])

const treeData = computed(() => {
  const fn = (routers: RouterItem[]): any => {
    return routers.flatMap(e => (e.meta?.fullPath && !(Array.isArray(e.meta.authorize) && e.meta.authorize.length === 0)
      ? [{
          title: e.meta?.title ?? e.meta.fullPath,
          key: e.meta.fullPath,
          children: fn(e.children || []),
          data: e,
        }]
      : []))
  }

  return [{
    title: '全部',
    key: '0',
    children: fn(routerAllMenu.value),
  }]
})

const spinning = ref(false)

async function save() {
  spinning.value = true
  const temps = deepCopy(checkedKeys.value).filter(v => v !== '0')
  await api.RolesManage.ModifyRoleMenu_PostAsync({ roleId }, temps).then((res) => {
    if (res) {
      message.success('保存成功')
    }
    spinning.value = false
  }).catch(() => {
    spinning.value = false
  })
}

watch(() => menuConfig, () => {
  checkedKeys.value = menuConfig
  console.log('%c [ treeData ]-25', 'font-size:13px; background:pink; color:#bf2c9f;', treeData)
}, { immediate: true })

defineExpose({ save })
</script>

<style scoped>

</style>
