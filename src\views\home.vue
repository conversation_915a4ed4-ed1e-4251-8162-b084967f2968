<template>
  <div class="home-container">
    <!-- 快捷导航区域 -->
    <div class="mx-auto mt-8 px-4 container">
      <h2 class="mb-4 border-l-4 border-blue-500 pl-2 text-lg font-medium">快捷导航</h2>
      <div class="grid grid-cols-2 gap-4 lg:grid-cols-4 md:grid-cols-4">
        <!-- 反馈管理 -->
        <div class="quick-nav-card" @click="navigateTo('/feedback-manage/')">
          <div class="flex flex-col items-center border border-blue-200 rounded-xl from-blue-50 to-blue-100 bg-gradient-to-br p-4 transition-all duration-300 hover:shadow-lg">
            <div class="mb-2 h-12 w-12 flex items-center justify-center rounded-lg bg-blue-500">
              <c-icon-message-outlined class="text-2xl text-white" />
            </div>
            <span class="text-sm text-gray-700 font-medium">反馈管理</span>
          </div>
        </div>
        <!-- 数据管理 -->
        <div class="quick-nav-card" @click="navigateTo('/data-manage/')">
          <div class="flex flex-col items-center border border-green-200 rounded-xl from-green-50 to-green-100 bg-gradient-to-br p-4 transition-all duration-300 hover:shadow-lg">
            <div class="mb-2 h-12 w-12 flex items-center justify-center rounded-lg bg-green-500">
              <c-icon-database-outlined class="text-2xl text-white" />
            </div>
            <span class="text-sm text-gray-700 font-medium">数据管理</span>
          </div>
        </div>
        <!-- 单位管理 -->
        <div class="quick-nav-card" @click="navigateTo('/contact-address-book/')">
          <div class="flex flex-col items-center border border-orange-200 rounded-xl from-orange-50 to-orange-100 bg-gradient-to-br p-4 transition-all duration-300 hover:shadow-lg">
            <div class="mb-2 h-12 w-12 flex items-center justify-center rounded-lg bg-orange-500">
              <c-icon-phone-outlined class="text-2xl text-white" />
            </div>
            <span class="text-sm text-gray-700 font-medium">单位管理</span>
          </div>
        </div>
        <!-- 日志管理 -->
        <div class="quick-nav-card" @click="navigateTo('/system-manage/log/')">
          <div class="flex flex-col items-center border border-red-200 rounded-xl from-red-50 to-red-100 bg-gradient-to-br p-4 transition-all duration-300 hover:shadow-lg">
            <div class="mb-2 h-12 w-12 flex items-center justify-center rounded-lg bg-red-500">
              <c-icon-file-text-outlined class="text-2xl text-white" />
            </div>
            <span class="text-sm text-gray-700 font-medium">日志管理</span>
          </div>
        </div>
        <!-- 基础数据管理 -->
        <div class="quick-nav-card" @click="navigateTo('/system-manage/standard/')">
          <div class="flex flex-col items-center border border-yellow-200 rounded-xl from-yellow-50 to-yellow-100 bg-gradient-to-br p-4 transition-all duration-300 hover:shadow-lg">
            <div class="mb-2 h-12 w-12 flex items-center justify-center rounded-lg bg-yellow-500">
              <c-icon-appstore-outlined class="text-2xl text-white" />
            </div>
            <span class="text-sm text-gray-700 font-medium">基础数据管理</span>
          </div>
        </div>
        <!-- 用户管理 -->
        <div class="quick-nav-card" @click="navigateTo('/system-manage/user/')">
          <div class="flex flex-col items-center border border-teal-200 rounded-xl from-teal-50 to-teal-100 bg-gradient-to-br p-4 transition-all duration-300 hover:shadow-lg">
            <div class="mb-2 h-12 w-12 flex items-center justify-center rounded-lg bg-teal-500">
              <c-icon-user-outlined class="text-2xl text-white" />
            </div>
            <span class="text-sm text-gray-700 font-medium">用户管理</span>
          </div>
        </div>
        <!-- 个人信息 -->
        <div class="quick-nav-card" @click="navigateTo('/user-center/')">
          <div class="flex flex-col items-center border border-cyan-200 rounded-xl from-cyan-50 to-cyan-100 bg-gradient-to-br p-4 transition-all duration-300 hover:shadow-lg">
            <div class="mb-2 h-12 w-12 flex items-center justify-center rounded-lg bg-cyan-500">
              <c-icon-user-outlined class="text-2xl text-white" />
            </div>
            <span class="text-sm text-gray-700 font-medium">个人信息</span>
          </div>
        </div>
        <!-- 门户站点 -->
        <div class="quick-nav-card" @click="navigateTo('/dept-client/')">
          <div class="flex flex-col items-center border border-gray-200 rounded-xl from-gray-50 to-gray-100 bg-gradient-to-br p-4 transition-all duration-300 hover:shadow-lg">
            <div class="mb-2 h-12 w-12 flex items-center justify-center rounded-lg bg-gray-500">
              <c-icon-home-outlined class="text-2xl text-white" />
            </div>
            <span class="text-sm text-gray-700 font-medium">门户站点</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="mx-auto mt-8 px-4 container">
      <div class="grid grid-cols-1 gap-4 md:grid-cols-4">
        <!-- 数据总量卡片 -->
        <div class="border border-gray-100 rounded-xl bg-white p-6 shadow-md">
          <div class="flex justify-between">
            <div class="font-medium">数据总量</div>
            <div class="rounded-md bg-blue-400 p-1">
              <div class="h-5 w-5 text-white">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-full w-full">
                  <circle cx="11" cy="11" r="8" />
                  <path d="m21 21-4.3-4.3" />
                </svg>
              </div>
            </div>
          </div>
          <div class="mt-4">
            <div class="text-3xl font-semibold">5500w+</div>
            <div class="mt-2 text-sm text-gray-400">近7天增长178w+ 篇</div>
          </div>
        </div>

        <!-- 涉我信息卡片 -->
        <div class="border border-gray-100 rounded-xl bg-white p-6 shadow-md">
          <div class="flex justify-between">
            <div class="font-medium">涉我信息</div>
            <div class="rounded-md bg-green-400 p-1">
              <div class="h-5 w-5 flex items-center justify-center text-white">i</div>
            </div>
          </div>
          <div class="mt-4">
            <div class="text-3xl font-semibold">16481</div>
            <div class="mt-2 text-sm text-gray-400">近7天增长 {{ countObj.global.swIncrement }} 篇</div>
          </div>
        </div>

        <!-- 今日更新卡片 -->
        <div class="border border-gray-100 rounded-xl bg-white p-6 shadow-md">
          <div class="flex justify-between">
            <div class="font-medium">今日更新</div>
            <div class="rounded-md bg-orange-400 p-1">
              <div class="h-5 w-5 flex items-center justify-center text-white">⏱</div>
            </div>
          </div>
          <div class="mt-4">
            <div class="text-3xl font-semibold">2088</div>
            <div class="mt-2 text-sm text-gray-400">较昨日 {{ (countObj.global.growthRate * 100).toFixed(1) }}%</div>
          </div>
        </div>

        <!-- 预警信息卡片 -->
        <div class="border border-gray-100 rounded-xl bg-white p-6 shadow-md">
          <div class="flex justify-between">
            <div class="font-medium">预警信息</div>
            <div class="rounded-md bg-red-400 p-1">
              <div class="h-5 w-5 text-white">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-full w-full">
                  <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9" />
                  <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0" />
                </svg>
              </div>
            </div>
          </div>
          <div class="mt-4">
            <div class="text-3xl font-semibold">{{ countObj.warning.total }}</div>
            <div class="mt-2 text-sm text-gray-400">
              较昨日
              <span :class="countObj.warning.yesterdayDiff < 0 ? 'text-red-500' : ''">
                {{ countObj.warning.yesterdayDiff }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 功能卡片 -->
    <div class="mx-auto mt-8 px-4 container">
      <div class="grid grid-cols-1 gap-4 md:grid-cols-5">
        <!-- 海外智库 -->
        <div class="cursor-pointer rounded-xl from-blue-500 to-blue-600 bg-gradient-to-r p-6 text-white shadow-lg" @click="navigateTo('/dept-client/overseas')">
          <div class="flex justify-between">
            <div>
              <h3 class="text-xl font-medium">海外智库</h3>
              <p class="mt-1 text-sm opacity-80">518696 智库文章采集与分析</p>
            </div>
            <div class="opacity-50">
              <div class="h-16 w-16">
                <div class="relative h-full w-full">
                  <div class="absolute bottom-0 left-0 right-0 w-16 border-t-2 border-white" />
                  <div class="absolute bottom-0 left-1/2 h-10 transform border-l-2 border-white -translate-x-1/2" />
                  <div class="absolute bottom-10 left-1/2 h-8 w-8 transform border-2 border-white rounded-full -translate-x-1/2" />
                </div>
              </div>
            </div>
          </div>
          <div class="mt-16 text-sm">今日更新: {{ countObj.global.hwzk?.value2 }} 篇</div>
        </div>

        <!-- 东盟新闻 -->
        <div class="cursor-pointer rounded-xl from-red-400 to-red-500 bg-gradient-to-r p-6 text-white shadow-lg" @click="navigateTo('/dept-client/news')">
          <div class="flex justify-between">
            <div>
              <h3 class="text-xl font-medium">东盟新闻媒体</h3>
              <p class="mt-1 text-sm opacity-80">125527 媒体新闻采集与分析</p>
            </div>
            <div class="opacity-50">
              <div class="h-16 w-16">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-full w-full">
                  <circle cx="12" cy="12" r="10" />
                  <path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20" />
                  <path d="M2 12h20" />
                </svg>
              </div>
            </div>
          </div>
          <div class="mt-16 text-sm">今日更新: {{ countObj.global.dmxw?.value2 }} 篇</div>
        </div>

        <!-- 东盟AI发展 -->
        <div class="cursor-pointer rounded-xl from-green-400 to-green-500 bg-gradient-to-r p-6 text-white shadow-lg" @click="navigateTo('/dept-client/ai')">
          <div class="flex justify-between">
            <div>
              <h3 class="text-xl font-medium">{{ countObj.global.ai?.table }}</h3>
              <p class="mt-1 text-sm opacity-80">{{ countObj.global.ai?.value1 }} 东盟人工智能政策与动态</p>
            </div>
            <div class="opacity-50">
              <div class="h-16 w-16">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-full w-full">
                  <circle cx="12" cy="12" r="10" />
                  <path d="M12 2a14.5 14.5 0 0 0 0 20a14.5 14.5 0 0 0 0-20" />
                  <path d="M2 12h20" />
                </svg>
              </div>
            </div>
          </div>
          <div class="mt-16 text-sm">今日更新: {{ countObj.global.ai?.value2 }} 篇</div>
        </div>

        <!-- 风险预警研判 -->
        <div class="cursor-pointer rounded-xl from-orange-400 to-orange-500 bg-gradient-to-r p-6 text-white shadow-lg" @click="navigateTo('/dept-client/risk')">
          <div class="flex justify-between">
            <div>
              <h3 class="text-xl font-medium">风险预警研判</h3>
              <p class="mt-1 text-sm opacity-80">东盟新闻与社会舆情分析</p>
            </div>
            <div class="opacity-50">
              <div class="h-16 w-16">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-full w-full">
                  <circle cx="12" cy="12" r="10" />
                  <path d="M12 2a14.5 14.5 0 0 0 0 20a14.5 14.5 0 0 0 0-20" />
                  <path d="M2 12h20" />
                </svg>
              </div>
            </div>
          </div>
          <div class="mt-16 text-sm">今日更新: {{ countObj.warning.today }} 篇</div>
        </div>

        <!-- Risk Forecast Research -->
        <div class="rounded-xl from-#c084fc to-#c084fc bg-gradient-to-r p-6 text-white shadow-lg">
          <div class="flex justify-between">
            <div>
              <h3 class="text-xl font-medium">谷歌数据库</h3>
              <p class="mt-1 text-sm opacity-80">5200万+</p>
            </div>
            <div class="opacity-50">
              <div class="h-16 w-16">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-full w-full"><circle cx="12" cy="12" r="10" /><path d="M12 2a14.5 14.5 0 0 0 0 20a14.5 14.5 0 0 0 0-20" /><path d="M2 12h20" /></svg>
              </div>
            </div>
          </div>
          <div class="mt-16 text-sm">近期新增 178w+ </div>
        </div>
      </div>
    </div>

    <!-- 近期关键信息 -->
    <div class="mx-auto mt-8 px-4 container">
      <div class="flex items-center justify-between">
        <h2 class="border-l-4 border-blue-500 pl-2 text-lg font-medium">近期关键信息</h2>
        <a class="cursor-pointer text-sm text-blue-500" @click="toKeyInformation">
          查看全部 &gt;
        </a>
      </div>

      <div v-if="countObj.hotData.length > 0" class="grid grid-cols-1 mt-4 gap-4 md:grid-cols-4">
        <div v-for="item in countObj.hotData" :key="item.id?.toString()" class="relative border border-gray-100 rounded-2xl bg-white p-4 shadow-md">
          <div v-show="item.isChina" class="absolute right-0 top-0 rounded-bl-2xl rounded-tr-2xl bg-red-100 px-2 py-1 text-xs text-red-500">涉我</div>

          <div class="text-xs text-gray-500">{{ item.sourceCountry }} <span class="ml-2">{{ item.source }}</span></div>
          <h3 class="mt-2 text-sm font-medium">{{ item.titleCn }}</h3>
          <p class="line-clamp-3 mt-2 text-xs text-gray-500">
            {{ item.contentCn }}
          </p>
          <div class="mt-3 flex items-center justify-between">
            <div class="text-xs text-gray-400">{{ formatDate(item.time) }}</div>
            <div class="rounded-2xl bg-red-500 px-2 py-1 text-xs text-white">热门</div>
          </div>
        </div>
      </div>
      <a-empty v-else />
    </div>

    <!-- 底部图表区域 -->
    <div class="mx-auto mb-8 mt-8 px-4 container">
      <div class="grid grid-cols-1 gap-8 md:grid-cols-4">
        <!-- 热点话题 -->
        <div>
          <h2 class="mb-4 border-l-4 border-blue-500 pl-2 text-lg font-medium">热点话题</h2>
          <div class="h-64 border border-gray-100 rounded-xl bg-white p-6 shadow-md">
            <div ref="wordCloudChartRef" class="h-full w-full" />
          </div>
        </div>

        <!-- 活跃智库 -->
        <div>
          <h2 class="mb-4 border-l-4 border-blue-500 pl-2 text-lg font-medium">活跃智库</h2>
          <div class="h-64 border border-gray-100 rounded-xl bg-white p-6 shadow-md">
            <div class="space-y-4">
              <div class="flex items-center">
                <div class="w-6 text-center">1.</div>
                <div class="flex-1">
                  <div class="text-sm font-medium">布鲁金斯学会</div>
                  <div class="flex justify-between text-xs text-gray-500">
                    <span>美国 | 北美地区 | 28篇</span>
                    <span class="text-blue-500">国际事务</span>
                  </div>
                </div>
              </div>

              <div class="flex items-center">
                <div class="w-6 text-center">2.</div>
                <div class="flex-1">
                  <div class="text-sm font-medium">兰德公司</div>
                  <div class="flex justify-between text-xs text-gray-500">
                    <span>美国 | 北美地区 | 18篇</span>
                    <div class="text-blue-500">国防安全</div>
                  </div>
                </div>
              </div>

              <div class="flex items-center">
                <div class="w-6 text-center">3.</div>
                <div class="flex-1">
                  <div class="text-sm font-medium">国际战略研究所</div>
                  <div class="flex justify-between text-xs text-gray-500">
                    <span>英国 | 欧洲地区 | 17篇</span>
                    <div class="text-blue-500">国际关系</div>
                  </div>
                </div>
              </div>

              <div class="flex items-center">
                <div class="w-6 text-center">4.</div>
                <div class="flex-1">
                  <div class="text-sm font-medium">德国国际经济研究所</div>
                  <div class="flex justify-between text-xs text-gray-500">
                    <span>德国 | 欧洲地区 | 12篇</span>
                    <div class="text-blue-500">全球经济</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 新闻分布与情感分析 -->
        <div class="md:col-span-2">
          <h2 class="mb-4 border-l-4 border-blue-500 pl-2 text-lg font-medium">东盟涉我新闻分布与情感分析</h2>
          <div class="h-64 border border-gray-100 rounded-xl bg-white p-6 shadow-md">
            <div class="h-full flex items-center">
              <div class="h-full w-1/2">
                <div ref="pieChartRef" class="h-full w-full" />
              </div>
              <div class="h-full w-1/2 flex flex-col justify-around py4">
                <div v-for="(item, index) in countObj.global.qgfx" :key="index" class="flex items-center space-y-2">
                  <div class="w-12 text-xs">{{ item.table }}</div>
                  <div class="relative ml-1 flex-1">
                    <div class="h-6 bg-gray-200">
                      <div v-if="item.table === '负面'" class="absolute h-6 flex items-center bg-red-500" style="width: 7%">
                        <span class="absolute left-1 text-xs text-white">{{ item.value1 }}</span>
                      </div>
                      <div v-else-if="item.table === '中性'" class="absolute h-6 flex items-center bg-yellow-500" style="width: 7%">
                        <span class="absolute left-1 text-xs text-white">{{ item.value1 }}</span>
                      </div>
                      <div v-else class="absolute h-6 flex items-center bg-green-500" style="width: 7%">
                        <span class="absolute left-1 text-xs text-white">{{ item.value1 }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { Chart, DataManageModelHotPageModel } from '@/api/models'
import { Api as api } from '@/api'
import { GlobalStatistics, RiskWarningStatsDto } from '@/api/models'

import { message } from 'ant-design-vue'
import { PieChart } from 'echarts/charts'
import { LegendComponent, TooltipComponent } from 'echarts/components'
import * as echarts from 'echarts/core'
import { LabelLayout } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import { useRouter } from 'vue-router'
import 'echarts-wordcloud'

echarts.use([TooltipComponent, LegendComponent, PieChart, CanvasRenderer, LabelLayout])

definePage({
  meta: {
    title: '首页',
    layout: 'admin',
    local: true,
    icon: 'HomeOutlined',
    layoutRoute: {
      meta: {
        title: '首页',
        icon: 'HomeOutlined',
        order: 9999,
      },
    },
  },
})

const pieChartRef = useTemplateRef('pieChartRef')
const wordCloudChartRef = useTemplateRef('wordCloudChartRef')

const countObj = ref<{ global: GlobalStatistics, hotData: DataManageModelHotPageModel[], hotTopic: Chart[], warning: RiskWarningStatsDto }>({
  global: new GlobalStatistics(),
  hotData: [],
  hotTopic: [],
  warning: new RiskWarningStatsDto(),
})

// 词云图数据
const wordCloudData = ref<{ name: string, value: number }[]>([])

const router = useRouter()

// 路由跳转函数
function navigateTo(path: string, open?: boolean) {
  if (open) {
    window.open(router.resolve(path).href)
  }
  else {
    router.push(path)
  }
}

// 初始化饼图
function initPieChart() {
  const color = ['#3B82F6', '#8B5CF6', '#F97316', '#6B7280']

  if (pieChartRef.value) {
    const chart = echarts.init(pieChartRef.value)

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      legend: {
        type: 'scroll',
        orient: 'horizontal',
        bottom: 'bottom',
        width: '80%',
        data: countObj.value.global.swfb?.map(item => item.table) || [],
        formatter(name: string | any[]) {
          return name.length > 6 ? `${name.slice(0, 6)}...` : name
        },
        tooltip: {
          show: true,
          formatter(param: { name: any }) {
            return param.name
          },
        },
        height: 50,
        padding: [0, 0, 0, 0],
        itemGap: 10,
        itemWidth: 10,
        itemHeight: 10,
      },
      series: [
        {
          name: '新闻分布',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '14',
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: countObj.value.global.swfb?.map((item, idx) => ({
            name: item.table,
            value: item.value1,
            itemStyle: { color: color[idx] },
          })) || [],
        },
      ],
    }

    chart.setOption(option)

    // 自动轮播显示tooltip
    const dataLen = option.series[0].data?.length || 0
    if (dataLen > 0) {
      let currentIndex = -1
      setInterval(() => {
        chart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: currentIndex,
        })
        currentIndex = (currentIndex + 1) % dataLen
        chart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: currentIndex,
        })
        chart.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: currentIndex,
        })
      }, 3000)
    }

    window.addEventListener('resize', () => {
      chart.resize()
    })
  }
}

// 初始化词云图
function initWordCloudChart() {
  if (wordCloudChartRef.value) {
    const chart = echarts.init(wordCloudChartRef.value)
    const option = {
      tooltip: {
        show: true,
      },
      series: [{
        type: 'wordCloud',
        left: 'center',
        top: 'center',
        width: '100%',
        height: '100%',
        right: null,
        bottom: null,
        sizeRange: [12, 25],
        rotationRange: [-45, 45],
        rotationStep: 45,
        gridSize: 8,
        drawOutOfBound: false,
        textStyle: {
          fontFamily: 'sans-serif',
          fontWeight: 'bold',
          color() {
            return `rgb(${[
              Math.round(Math.random() * 160),
              Math.round(Math.random() * 160),
              Math.round(Math.random() * 160),
            ].join(',')})`
          },
        },
        emphasis: {
          focus: 'self',
          textStyle: {
            textShadowBlur: 10,
            textShadowColor: '#333',
          },
        },
        data: wordCloudData.value,
      }],
    }
    chart.setOption(option)

    window.addEventListener('resize', () => {
      chart.resize()
    })
  }
}

// 获取统计数据
async function getCount() {
  try {
    const res = await api.Statistices.Statistics_GetAsync({})
    const res1 = await api.Statistices.GetHotDataManageAsync({})
    const res2 = await api.Statistices.GetHotTopicAsync({ pageIndex: 10 })
    countObj.value.warning = await api.Statistices.RiskWarningStatistics_GetAsync()

    // 处理词云数据，过滤掉空值
    wordCloudData.value = res2
      .filter(item => item.table && item.value1)
      .map(item => ({
        name: item.table || '',
        value: Number(item.value1) || 0,
      }))

    countObj.value.global = res
    countObj.value.hotData = res1
    countObj.value.hotTopic = res2

    initWordCloudChart()
  }
  catch (error: any) {
    message.error(error.message)
  }
}

// 格式化日期
function formatDate(date: string | Date | null | undefined) {
  if (!date)
    return ''
  const d = new Date(date)
  return d.toLocaleDateString('zh-CN')
}

// 跳转到关键信息页面
function toKeyInformation() {
  router.push({ path: '/dept-client/key-information' })
}

onMounted(() => {
  getCount()
  initPieChart()
})
</script>

<style scoped lang="less">
.home-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.quick-nav-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-nav-card:hover {
  transform: translateY(-2px);
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 卡片悬停效果 */
.shadow-md {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.shadow-md:hover {
  transform: translateY(-4px);
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.15),
    0 4px 6px -2px rgba(0, 0, 0, 0.1);
}

.shadow-lg {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.shadow-lg:hover {
  transform: translateY(-4px);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.2),
    0 10px 10px -5px rgba(0, 0, 0, 0.1);
}

/* 特色卡片渐变背景过渡效果 */
.from-blue-500.to-blue-600:hover,
.from-red-400.to-red-500:hover,
.from-green-400.to-green-500:hover,
.from-orange-400.to-orange-500:hover {
  filter: brightness(1.1);
  cursor: pointer;
}

/* 新闻卡片标题悬停效果 */
.rounded-2xl h3 {
  transition: color 0.3s ease;
}

.rounded-2xl:hover h3 {
  color: #3b82f6;
}

/* 查看全部链接悬停效果 */
.text-blue-500 {
  transition: all 0.3s ease;
}

.text-blue-500:hover {
  color: #2563eb;
  text-decoration: underline;
}

.rounded-xl {
  border-radius: 0.75rem;
}
</style>
