<template>
  <header class="text-center">
    <div class="mb-4">
      <h3 class="mb-1 text-center text-base">{{ detail.title || '-' }}</h3>
      <h3 class="text-center text-base">{{ detail.titleCn || '-' }}</h3>
    </div>
    <div class="text-sm c-text-secondary">
      <span>发布时间：{{ dateTime(detail.time) }}</span>
      <span class="ml-4">来源：{{ detail.source || '-' }}</span>
      <span class="ml-4">作者：{{ detail.author || '-' }}</span>
    </div>
  </header>
  <article class="mt-16 flex justify-between">
    <div class="flex-2">
      <div class="flex justify-between">
        <a-radio-group v-model:value="translate" button-style="solid">
          <a-radio-button value="AI双语翻译">AI双语翻译</a-radio-button>
          <a-radio-button value="网页快照">网页快照</a-radio-button>
        </a-radio-group>
        <div>
          <a-button v-show="detail.link" class="text-base c-primary" type="link" target="_blank" :href="detail.link!">#原文链接</a-button>
          <a-button class="text-base c-error" type="link" danger :icon="h(ToolOutlined)" @click="onFeedback">问题反馈</a-button>
        </div>
      </div>
      <article class="text-base">
        <div v-if="detail.contentCn">
          <div v-for="(text, index) in detail.contentCn.split('\n')" :key="index" class="indent-2rem line-height-8" :class="index % 2 === 0 ? 'mt-4' : 'mt-2'">
            {{ text }}
          </div>
        </div>
        <div v-else class="c-text-secondary">暂无内容</div>
      </article>
    </div>
    <div class="ml-6 min-w-400px flex-1 border-#2c628e57 border-l-solid pl-6 text-base">
      <div>
        <div class="w-100px flex items-end border-(1px #E5E5E5 solid) rounded-2 bg-[rgba(255,255,255,0.73)] p-2 c-#7948EA">
          <div class="i-material-symbols-voice-chat-outline" />
          <div class="ml-2">AI总结</div>
        </div>
        <div class="mt-4">
          <p>
            {{ detail.ai || '暂无AI总结' }}
          </p>
        </div>
      </div>
      <div class="mt-8 text-base">
        <div class="flex">
          <span class="w-120px text-right font-500">情感倾向：</span>
          <span class="c-error">{{ detail.emotion || '-' }}</span>
        </div>
        <div class="mt-2 flex">
          <span class="w-120px text-right font-500">涉及国家/地区：</span>
          <span class="c-error">{{ detail.region?.length ? detail.region.map(item => item.name).join('、') : '-' }}</span>
        </div>
        <div class="mt-2 flex">
          <span class="w-120px text-right font-500">涉及领域：</span>
          <span class="c-primary">{{ detail.domain?.length ? detail.domain.map(item => item.name).join('、') : '-' }}</span>
        </div>
      </div>
      <div class="mt-8 text-center"><a-button type="primary" :icon="h(RedoOutlined)" ghost>重新生成</a-button></div>
    </div>
  </article>

  <FeedbackModal v-model:visible="showFeedback" :data-id="feedbackDataId" @success="showFeedback = false" />
</template>

<script lang='ts' setup>
import { Api as api } from '@/api'
import { DataManageModel } from '@/api/models'
import { RedoOutlined, ToolOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useRoute } from 'vue-router'

const translate = ref('AI双语翻译')

const route = useRoute()

const detail = ref(new DataManageModel())

async function getData(id: string) {
  try {
    detail.value = await api.DataManageModels.FindOneById_GetAsync({ id })
  }
  catch (error: any) {
    message.error(`获取数据失败${error.message}`)
  }
}

const showFeedback = ref(false)
const feedbackDataId = ref('')

function onFeedback() {
  showFeedback.value = true
  feedbackDataId.value = detail.value.id as string
}

onMounted(() => {
  if (route.query?.id) {
    getData(route.query?.id as string)
  }
  else {
    window.close()
  }
})
</script>

<style scoped>

</style>
