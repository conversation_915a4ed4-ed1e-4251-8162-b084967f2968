<template>
  <div>
    <ViewFile v-if="showList" :files="fileList" show-remove @remove="remove" />

    <a-button
      v-if="showUpload"
      type="dashed" block class="mt2 size-16" @click="upload"
    >
      <template #icon>
        <c-icon-plus-outlined />
      </template>
      上传
    </a-button>
  </div>
</template>

<script setup lang="ts">
import type { UploadFileInfo } from '@/api/models'
import type FileManager from '@/components/FileManager/FileManager.vue'
import { Api } from '@/api'
import { FileAttribution, FileType } from '@/api/models'

const { showList = true, showUpload = true, autoList = false, config = { multiple: true, immediateReturn: true, menu: [FileType.文档, FileType.图片, FileType.视频], fileAttribution: FileAttribution.管理认证 } }
= defineProps<{ showUpload?: boolean, autoList?: boolean, showList?: boolean, config?: InstanceType<typeof FileManager>['$props'] }>()

/** 要先传入文件列表 */
const fileList = defineModel<UploadFileInfo[]>('fileList', { default: () => [] })

const fileIds = defineModel<GUID | string | GUID[] | string[]>('value')

watch(fileIds, async (val) => {
  let ids = [] as GUID[]
  if (Array.isArray(val)) {
    ids = fileIds.value as any
  }
  else if (fileIds.value) {
    ids.push(fileIds.value as any)
  }

  if (autoList) {
    fileList.value = []
    if (ids.length > 0) {
      const controller = new AbortController()
      fileList.value = await Api.FileManage.GetFilesByIds_PostAsync(ids as any, { signal: controller.signal })
      onWatcherCleanup(controller.abort)
    }
  }
}, { immediate: true })

function remove(id: string) {
  fileList.value = fileList.value.filter(v => v.id !== id)
  if (config.multiple && Array.isArray(fileIds.value)) {
    fileIds.value = fileIds.value?.filter(v => v !== id)
  }
  else {
    fileIds.value = null
  }
}

function upload() {
  useFileMangerModal((files) => {
    if (config.multiple) {
      fileIds.value = [...fileIds.value || [], ...files.map(v => v.id)] as any
      fileList.value = [...fileList.value || [], ...files] as any
    }
    else {
      fileIds.value = files[0]?.id
      fileList.value = files
    }
  }, config)
}
</script>

<style scoped>

</style>
