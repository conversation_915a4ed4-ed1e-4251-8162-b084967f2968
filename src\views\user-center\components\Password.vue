<!--
 * @Author: Ztq
 * @Date: 2023-06-01 11:34:26
 * @LastEditors: 景 彡
 * @LastEditTime: 2024-11-13 16:28:43
 * @Description:
 * @FilePath: \ch2-template-vue\src\views\user-center\components\Password.vue
-->
<template>
  <c-modal
    :open="visible"
    :style="{ top: '0px' }"
    :closable="true"
    title="修改密码"
    :destroy-on-close="true"
    width="40%"
    @cancel="cancel()"
  >
    <template #footer>
      <a-button type="default" @click="cancel()">
        取消
      </a-button>
      <a-button type="primary" @click="handleOk()">
        确认修改
      </a-button>
    </template>
    <a-form ref="formRef" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :model="formData" :rules="rules">
      <a-form-item label="原密码" name="oldPassword">
        <c-input-password
          v-model:value="formData.oldPassword"
          placeholder="请输入原密码"
          :maxlength="16"
        />
      </a-form-item>
      <a-form-item label="新密码" name="password">
        <c-input-password
          v-model:value="formData.password"
          placeholder="请输入新密码"
          :maxlength="16"
        />
        <a-progress
          style="width: 100%"
          :percent="currentStep"
          :steps="step"
          :stroke-color="stepColor"
          :format="strength"
        />
      </a-form-item>
      <a-form-item label="确认密码" name="passwordConfirm">
        <c-input-password
          v-model:value="formData.passwordConfirm"
          placeholder="请输入确认密码"
          :maxlength="16"
        />
      </a-form-item>
    </a-form>
  </c-modal>
</template>

<script setup lang="ts">
import type { FormInstance } from 'ant-design-vue'
import type { Rule, RuleObject } from 'ant-design-vue/es/form'
import { Api as api } from '@/api'
import { FormValidator } from '@/utils/rules'
import {} from '@ant-design/icons-vue'
import { Form, message } from 'ant-design-vue'
import { computed, reactive, ref, watch } from 'vue'

// 校验表单
const formRef = ref<FormInstance>()
// 控制弹窗是否显示
const visible = ref(false)
// 步骤条组件总步数
const step = ref(5)
// 当前进度
const currentStep = ref<number>(0)
// 根据进度步骤条分别显示不同颜色
const stepColor = computed(() => (currentStep.value <= 40 ? 'red' : currentStep.value <= 80 ? '#f75300' : '#29f700'))
// 步骤条文本数组
const strengthArr = ref(['非常弱', '弱', '中等', '强', '非常强'])
// 步骤条当前文本
function strength() {
  return strengthArr.value[currentStep.value / 20 - 1]
}

/**
 * 方法：校验密码
 * @param _rule Rule
 * @param value 当前值
 * @description 校验密码是否为空
 */
async function validatePass(_rule: Rule, value: string) {
  if (!value)
    return Promise.reject(new Error('请输入密码'))

  return Promise.resolve()
}

// 密码表单
const formData = reactive<{
  oldPassword: string
  password: string
  passwordConfirm: string
}>({
  oldPassword: '',
  password: '',
  passwordConfirm: '',
})

/**
 * 方法：校验密码
 * @param _rule Rule
 * @param value 当前值
 * @description 校验确认密码，确认密码不可为空并且需要与新密码一致
 */
async function validateConfirmPass(_rule: Rule, value: string) {
  if (!value) {
    console.log('formRef.value', formRef.value)
    return Promise.reject(new Error('请输入确认密码'))
  }
  if (formData.password !== formData.passwordConfirm)
    return Promise.reject(new Error('两次输入密码不一致'))

  return Promise.resolve()
}

// 校验规则
const rules = reactive<Record<string, RuleObject | RuleObject[]>>({
  oldPassword: [{ required: true, validator: validatePass, trigger: 'change' }],
  password: [
    {
      required: true,
      validator: FormValidator(
        'isPasswordReg',
        '该密码为弱密码，请按要求修改密码 :至少两种组合(数字、字母、特殊字符), 长度为8-16位字符',
      ),
      trigger: 'change',
    },
  ],
  passwordConfirm: [{ required: true, validator: validateConfirmPass, trigger: 'change' }],
})

// 定义表单校验对象
const { useForm } = Form

const { resetFields } = useForm(formData, rules, {})

function cancel() {
  resetFields()
  currentStep.value = 0
  visible.value = false
}

/**
 * @function 确认修改密码
 * @description 修改密码并关闭窗口
 */
function handleOk() {
  formRef.value?.validate().then((res) => {
    if (res) {
      api.CurrentUser.EditUserPassword_PostAsync({ oldPassword: formData.oldPassword, password: formData.password })
      message.success('修改密码成功！')
      cancel()
    }
  })
}

/**
 * @function 监听事件
 * @description 监听新密码变化控制进度条
 */
watch(
  () => formData.password,
  (newValue) => {
    const check = chackPasswordStrength(newValue.toString())
    if (newValue && newValue.length > 0)
      currentStep.value = (check + 1) * 20
    else
      currentStep.value = 0
  },
)

/**
 * @function 检查密码复杂度
 * @param password 新密码
 * @description 判断密码强度，分5个等级
 */
function chackPasswordStrength(password: string) {
  const patterns = [/\d/, /[a-z]/, /[A-Z]/, /[^a-z\d]/i]
  const count = patterns.reduce((acc, pattern) => acc + Number(pattern.test(password)), 0)
  const len = password.length
  return len < 8 ? 0 : len >= 16 && count >= 3 ? 4 : count < 3 ? (len < 12 ? 1 : 3) : len < 12 ? 2 : 3
}

defineExpose({
  visible,
})
</script>

<style scoped lang="less">
:deep(.ant-progress-steps-item) {
  min-width: 15%;
}
</style>
