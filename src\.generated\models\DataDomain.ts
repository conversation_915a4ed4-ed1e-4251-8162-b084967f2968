import { DataManageModel } from "./DataManageModel";
import { DataAndDomain } from "./DataAndDomain";
import { DomainGroup } from "./DomainGroup";
export class DataDomain {
  id: GUID = "00000000-0000-0000-0000-000000000000";
  name?: string | null | undefined = null;
  dataManages?: DataManageModel[] | null | undefined = [];
  /**涉及地区*/
  dataAndDomain?: DataAndDomain[] | null | undefined = [];
  groupId?: GUID = null;
  group?: DomainGroup | null | undefined = null;
}
